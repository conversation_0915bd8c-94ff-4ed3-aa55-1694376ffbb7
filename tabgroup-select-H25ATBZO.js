import { a as TabGroupCreateElement } from "./chunk-T7ZIDMMC.js";
import { e as fetchTabGroups, o as html } from "./chrome-utils.js";
import "./chunk-EK7ODJWE.js";

/**
 * Custom element for selecting and managing tab groups.
 * Handles grouping tabs, rendering group list, and creating new groups.
 */
class TabGroupSelectElement extends HTMLElement {
  /**
   * @param {number[]} tabIds - Array of tab IDs to be grouped.
   */
  constructor(tabIds) {
    super();
    /** @type {number[]} */
    this.tabIds = tabIds;
  }

  /**
   * Groups the current tabs into an existing group.
   * @param {number} groupId - The ID of the group to add tabs to.
   * @returns {Promise<void>}
   */
  async groupTabsInExistingGroup(groupId) {
    try {
      await chrome.tabs.group({ tabIds: this.tabIds, groupId });
      $("marked-action")?.remove();
      notify("add in tabgroup");
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * Handles selection of a tab group or creation of a new group.
   * @param {Event} event - The click event from the group list.
   */
  onTabGroupSelect = ({ target }) => {
    const groupId = +target.closest("li").id;
    if (groupId) {
      return this.groupTabsInExistingGroup(groupId);
    }
    this.after(new TabGroupCreateElement(null, this.tabIds));
  };

  /**
   * Renders the list of tab groups and the option to create a new group.
   * @returns {import('lit-html').TemplateResult}
   */
  render() {
    const renderGroupItem = (group) => html`
      <li id=${group.id}>
        <cite>${group.title}</cite>
        <tv-icon ico="edit" title="edit group"></tv-icon>
      </li>
    `;
    return html`
      ${this.tabGroups.map(renderGroupItem)}
      <li>${i18n("create_group")}</li>
    `;
  }

  /**
   * Lifecycle method called when the element is added to the DOM.
   * Initializes group list and event listeners.
   * @returns {Promise<void>}
   */
  async connectedCallback() {
    this.id = "tabgroup-list";
    this.style.left = "4em";
    this.setAttribute("popover", "");
    this.tabGroups = await fetchTabGroups({});
    this.replaceChildren(this.render());
    this.showPopover();
    $on(this, "click", this.onTabGroupSelect);
    $on(this, "toggle", (event) => event.newState === "closed" && this.remove());
  }
}

// Register the custom element
customElements.define("tabgroup-select", TabGroupSelectElement);
export { TabGroupSelectElement as TabgroupSelect };
