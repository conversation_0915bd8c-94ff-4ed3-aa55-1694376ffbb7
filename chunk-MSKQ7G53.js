// TabGroupRule.js

/**
 * Represents a rule for grouping browser tabs.
 * @class
 */
class TabGroupRule {
  /**
   * @param {Object} urlMatches - URL match patterns for the rule.
   * @param {string[]} titleIncludes - List of substrings to match in tab titles.
   * @param {string} color - Color for the tab group.
   * @param {string} [name=""] - Name of the tab group rule.
   * @param {string} [contextMenuGroupTitle=""] - Title for the context menu group.
   */
  constructor(urlMatches, titleIncludes, color, name = "", contextMenuGroupTitle = "") {
    /** @type {string} Unique identifier for the rule. */
    this.id = "r" + Math.random().toString(36).slice(2);
    /** @type {string} Name of the rule. */
    this.name = name;
    /** @type {string} Title for the context menu group. */
    this.ctmGroupTitle = contextMenuGroupTitle;
    /** @type {string} Color for the tab group. */
    this.color = color ?? "grey";
    /** @type {Object} URL match patterns. */
    this.urlMatches = urlMatches ?? {};
    /** @type {string[]} Substrings to match in tab titles. */
    this.titleIncludes = titleIncludes ?? [];
    /** @type {number} Priority of the rule. */
    this.priority = 0;
    /** @type {boolean} Whether the rule is enabled. */
    this.enabled = true;
    /** @type {number} Timestamp when the rule was created. */
    this.createdAt = Date.now();
    /** @type {number} Timestamp when the rule was last modified. */
    this.lastModifiedAt = Date.now();
  }
}

/**
 * Object store names for IndexedDB.
 * @readonly
 * @enum {string}
 */
const TAB_GROUP_RULES_STORE = {
  TabGroupRules: "TabGroupRules"
};

/**
 * Handles the IndexedDB upgrade event by creating the object store.
 * @param {IDBVersionChangeEvent} event
 */
function handleUpgrade(event) {
  event.target.result.createObjectStore(TAB_GROUP_RULES_STORE.TabGroupRules, { keyPath: "id" });
}

/**
 * Opens the IndexedDB database, creating it if necessary.
 * @returns {Promise<IDBDatabase>}
 */
function openDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open("tabVertikal", 1);
    request.onupgradeneeded = handleUpgrade;
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
    request.onblocked = () => console.warn("Pending until unblocked");
  });
}

/**
 * Retrieves all tab group rules from the database.
 * @returns {Promise<TabGroupRule[]>}
 */
async function getAllTabGroupRules() {
  return new Promise((resolve, reject) => {
    openDatabase().then((db) => {
      const transaction = db
        .transaction(TAB_GROUP_RULES_STORE.TabGroupRules, "readonly")
        .objectStore(TAB_GROUP_RULES_STORE.TabGroupRules)
        .getAll();
      transaction.onsuccess = ({ target }) => resolve(target.result);
      transaction.onerror = (event) => reject(event);
      db.close();
    });
  });
}

/**
 * Checks if at least one tab group rule exists in the database.
 * @returns {Promise<boolean>}
 */
async function hasAnyTabGroupRule() {
  return new Promise((resolve, reject) => {
    openDatabase().then((db) => {
      const countRequest = db
        .transaction(TAB_GROUP_RULES_STORE.TabGroupRules, "readonly")
        .objectStore(TAB_GROUP_RULES_STORE.TabGroupRules)
        .count();
      countRequest.onsuccess = ({ target }) => resolve(target.result >= 1);
      countRequest.onerror = (event) => reject(event);
      db.close();
    });
  });
}

/**
 * Adds or updates a tab group rule in the database.
 * @param {TabGroupRule} rule
 * @returns {Promise<TabGroupRule>}
 */
async function saveTabGroupRule(rule) {
  return new Promise((resolve, reject) => {
    openDatabase().then((db) => {
      const request = db
        .transaction(TAB_GROUP_RULES_STORE.TabGroupRules, "readwrite")
        .objectStore(TAB_GROUP_RULES_STORE.TabGroupRules)
        .put(rule);
      request.onsuccess = () => resolve(rule);
      request.onerror = (event) => reject(event);
      db.close();
    });
  });
}

/**
 * Updates a specific property of a tab group rule.
 * @param {string} ruleId - The ID of the rule to update.
 * @param {string} property - The property name to update.
 * @param {*} value - The new value for the property.
 * @returns {Promise<TabGroupRule>}
 */
async function updateTabGroupRuleProperty(ruleId, property, value) {
  return new Promise((resolve, reject) => {
    openDatabase().then((db) => {
      const objectStore = db
        .transaction(TAB_GROUP_RULES_STORE.TabGroupRules, "readwrite")
        .objectStore(TAB_GROUP_RULES_STORE.TabGroupRules);
      const getRequest = objectStore.get(ruleId);
      getRequest.onsuccess = ({ target }) => {
        const rule = target.result;
        rule[property] = value;
        rule.lastModifiedAt = Date.now();
        const putRequest = objectStore.put(rule);
        putRequest.onsuccess = () => resolve(rule);
        putRequest.onerror = (event) => reject(event);
      };
      getRequest.onerror = (event) => reject(event);
      db.close();
    });
  });
}

/**
 * Deletes a tab group rule from the database.
 * @param {string} ruleId
 * @returns {Promise<void>}
 */
async function deleteTabGroupRule(ruleId) {
  return new Promise((resolve, reject) => {
    openDatabase().then((db) => {
      const request = db
        .transaction(TAB_GROUP_RULES_STORE.TabGroupRules, "readwrite")
        .objectStore(TAB_GROUP_RULES_STORE.TabGroupRules)
        .delete(ruleId);
      request.onsuccess = ({ target }) => resolve(target.result);
      request.onerror = (event) => reject(event);
      db.close();
    });
  });
}

/**
 * Custom dialog element for reporting errors via Gmail.
 * @class
 * @extends HTMLDialogElement
 */
class ReportBugDialog extends HTMLDialogElement {
  /**
   * @param {Error} error - The error to report.
   */
  constructor(error) {
    super();
    /** @type {Error} */
    this.error = error;
    /** @type {string} */
    this.gmailUrl = "https://mail.google.com/mail/u/0/";
    /** @type {string} */
    this.mailTo = "<EMAIL>";
  }

  /**
   * Opens a Gmail compose window with error details pre-filled.
   * @returns {Promise<void>}
   */
  async reportBug() {
    const { version, short_name } = chrome.runtime.getManifest();
    const stack = this.error.stack?.replaceAll("\n", "%0A");
    const url = `${this.gmailUrl}?to=${this.mailTo}&su=${this.error.message}&body=Error:%20${this.error.message}%0AStack:%20${stack}%0AExtensionId:%20${chrome.runtime.id}%0AExtension%20Name:%20${short_name}%0AExtension%20Version:%20${version}%0ABrowser%20Info:%20${navigator.userAgent}&fs=1&tf=cm`;
    await chrome.tabs.create({ url });
  }

  /**
   * Renders the dialog's HTML content.
   * @returns {string}
   */
  render() {
    return `<h2>\u{1F62A} ${chrome.i18n.getMessage("sorry_something_went_wrong")} \u{1F41B}</h2>
			<p style="max-width: 50ch;overflow-wrap: break-word;">\u274C ${this.error.message?.replaceAll("<", "<")?.replaceAll(">", ">")}</p>
			<button style="font-size: 1rem;margin-inline: auto;display: block;"> 
				\u{1F41E} <span>${chrome.i18n.getMessage("report_bug")}</span>
			</button>
			<div style="text-align:center;font-size:x-small;">${chrome.i18n.getMessage("just_one_click")}</div>`;
  }

  /**
   * Lifecycle callback when the element is added to the DOM.
   */
  connectedCallback() {
    this.id = "report-bug";
    this.innerHTML = this.render();
    this.showModal();
    this.lastElementChild.previousElementSibling.addEventListener(
      "click",
      this.reportBug.bind(this)
    );
  }
}

// Register the custom element for error reporting.
customElements.define("report-bug", ReportBugDialog, { extends: "dialog" });

// Export all public API with clear names.
export {
  TabGroupRule,
  getAllTabGroupRules,
  hasAnyTabGroupRule,
  saveTabGroupRule,
  updateTabGroupRuleProperty,
  deleteTabGroupRule,
  ReportBugDialog
};
