import "./chunk-EK7ODJWE.js";
import emojiPickerStyles from "./emoji-picker-KRZLCODK.css" with { type: "css" };

/**
 * EmojiPicker is a unified custom HTML element that provides a UI for selecting emojis.
 * It fetches emoji data, displays categories, and allows users to insert emojis into an input.
 * Consolidates functionality from multiple duplicate implementations.
 * 
 * @element emoji-picker
 */
class EmojiPicker extends HTMLElement {
  /**
   * Stores the emoji data categorized by type.
   * @type {Record<string, string[]>}
   */
  emojis = {};

  constructor() {
    super();
    // Attach shadow DOM and apply styles
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [emojiPickerStyles];
  }

  /**
   * Renders the HTML structure for the emoji picker.
   * @returns {string} HTML string for the picker UI
   */
  render() {
    return `
      <header class="category-bar">
        <div data-ctg="animals">\u{1F436}</div>
        <div data-ctg="food_drinks">\u{1F355}</div>
        <div data-ctg="travels">\u{1F680}</div>
        <div data-ctg="objects">\u{1F389}</div>
        <div data-ctg="symbols">\u2764\uFE0F</div>
        <div data-ctg="flags">\u{1F3C1}</div>
      </header>
      <article class="emoji-container">
        <div class="emoji-category" id="animals"></div>
        <div class="emoji-category" id="food_drinks"></div>
        <div class="emoji-category" id="travels"></div>
        <div class="emoji-category" id="objects"></div>
        <div class="emoji-category" id="symbols"></div>
        <div class="emoji-category" id="flags"></div>
      </article>
    `;
  }

  /**
   * Populates the emoji container for a given category.
   * If already populated, just scrolls into view.
   * @param {string} category - The emoji category to populate
   */
  async populateCategory(category) {
    const container = this.shadowRoot.getElementById(category);
    
    // If already populated, just scroll into view
    if (container.hasChildNodes()) {
      container.scrollIntoView({ behavior: "smooth", block: "start" });
      return;
    }

    // Load emojis for this category if not already loaded
    if (!this.emojis[category]) {
      await this.loadEmojis();
    }

    // Populate the container with emojis
    const emojisInCategory = this.emojis[category] || [];
    container.innerHTML = emojisInCategory
      .map(emoji => `<span class="emoji" data-emoji="${emoji}">${emoji}</span>`)
      .join("");

    // Scroll into view
    container.scrollIntoView({ behavior: "smooth", block: "start" });
  }

  /**
   * Loads emoji data from the API.
   * @returns {Promise<void>}
   */
  async loadEmojis() {
    try {
      // Try to load from cache first
      const cached = localStorage.getItem("emoji-data");
      if (cached) {
        this.emojis = JSON.parse(cached);
        return;
      }

      // Fetch from API
      const response = await fetch("https://emoji-api.com/emojis?access_key=YOUR_ACCESS_KEY");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Group emojis by category
      this.emojis = data.reduce((acc, emoji) => {
        const category = emoji.group || "symbols";
        if (!acc[category]) acc[category] = [];
        acc[category].push(emoji.character);
        return acc;
      }, {});

      // Cache the data
      localStorage.setItem("emoji-data", JSON.stringify(this.emojis));
    } catch (error) {
      console.warn("Failed to load emojis from API, using fallback:", error);
      // Fallback emoji data
      this.emojis = {
        animals: ["🐶", "🐱", "🐭", "🐹", "🐰", "🦊", "🐻", "🐼", "🐨", "🐯"],
        food_drinks: ["🍕", "🍔", "🍟", "🌭", "🍿", "🧄", "🥕", "🌽", "🌶️", "🥒"],
        travels: ["🚗", "🚕", "🚙", "🚌", "🚎", "🏎️", "🚓", "🚑", "🚒", "🚐"],
        objects: ["🎉", "🎊", "🎈", "🎁", "🎀", "🎂", "🍰", "🧁", "🥧", "🍭"],
        symbols: ["❤️", "💙", "💚", "💛", "🧡", "💜", "🖤", "🤍", "🤎", "💔"],
        flags: ["🏁", "🏳️", "🏴", "🏳️‍🌈", "🏳️‍⚧️", "🇺🇸", "🇬🇧", "🇫🇷", "🇩🇪", "🇯🇵"]
      };
    }
  }

  /**
   * Handles emoji selection and insertion into the target input.
   * @param {Event} event - The click event
   */
  onEmojiSelect(event) {
    const emoji = event.target.dataset.emoji;
    if (!emoji) return;

    // Find the target input element
    const targetInput = document.querySelector('input[type="text"]:focus, textarea:focus') 
                     || document.activeElement;
    
    if (targetInput && (targetInput.tagName === 'INPUT' || targetInput.tagName === 'TEXTAREA')) {
      // Insert emoji at cursor position
      const start = targetInput.selectionStart;
      const end = targetInput.selectionEnd;
      const value = targetInput.value;
      
      targetInput.value = value.slice(0, start) + emoji + value.slice(end);
      targetInput.selectionStart = targetInput.selectionEnd = start + emoji.length;
      
      // Dispatch input event to trigger any listeners
      targetInput.dispatchEvent(new Event('input', { bubbles: true }));
    }

    // Hide the picker
    this.style.display = 'none';
  }

  /**
   * Lifecycle callback when the element is connected to the DOM.
   */
  async connectedCallback() {
    // Render the UI
    this.shadowRoot.innerHTML = this.render();

    // Load initial emoji data
    await this.loadEmojis();

    // Set up event listeners
    this.shadowRoot.addEventListener("click", (event) => {
      const categoryHeader = event.target.closest("[data-ctg]");
      if (categoryHeader) {
        const category = categoryHeader.dataset.ctg;
        this.populateCategory(category);
        return;
      }

      const emojiElement = event.target.closest(".emoji");
      if (emojiElement) {
        this.onEmojiSelect(event);
      }
    });

    // Populate the first category by default
    await this.populateCategory("animals");
  }
}

// Register the custom element
customElements.define("emoji-picker", EmojiPicker);

export { EmojiPicker };
