import { createTab, getWindowTitles, html } from "./chrome-utils.js";
import "./chunk-EK7ODJWE.js";

/**
 * Unified utility class for exporting tab data in various formats.
 * Consolidates functionality from multiple duplicate implementations.
 */
class TabExportUtility {
  /**
   * Stores window titles for HTML export.
   * @type {Record<number, string>}
   */
  winTitles = {};

  constructor() {}

  /**
   * Converts tab data to JSON string.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string}
   */
  toJSON(windowData) {
    const simplifiedTabs = windowData.tabs.map(({ title, url, favIconUrl }) => ({
      title,
      url,
      favIconUrl,
    }));
    return JSON.stringify(simplifiedTabs);
  }

  /**
   * Converts tab data to CSV format.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string}
   */
  toCSV(windowData) {
    const header = `url,title,favIconUrl
`;
    const rows = windowData.tabs
      .map(({ title, url, favIconUrl }) => `${url},${title},${favIconUrl}`)
      .join(`
`);
    return header + rows;
  }

  /**
   * Converts tab data to Markdown format.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string}
   */
  toMarkdown(windowData) {
    return windowData.tabs
      .map(({ title, url }) => `[${title}](${url})`)
      .join(`
`);
  }

  /**
   * Converts tab data to HTML format, including window titles.
   * @param {Array<{ id: number, tabs: Array<{title: string, url: string, favIconUrl: string}> }>} windows
   * @returns {string}
   */
  toHTML(windows) {
    const docStart = `<!DOCTYPE html>
		<html lang="en">
		<head>
			<meta charset="UTF-8">
			<meta name="viewport" content="width=device-width, initial-scale=1.0">
			<title>Tabs at ${new Date().toLocaleString("default", { dateStyle: "full" })}</title>
		</head>
		<body>
		`;

    // Render a single tab as a list item
    const renderTab = (tab) => `<li style="padding-block: 0.2em">
            <a href="${tab.url}" target="_blank" rel="noopener noreferrer">${tab.title}</a>
        </li>`;

    // Render a window section with its tabs
    const renderWindow = (window) => `<details>
            <summary>${this.winTitles[window.id] ?? window.id}</summary>
            <ol>
                ${window.tabs.map(renderTab).join("")}
            </ol>
        </details>`;

    return `${docStart}<blockquote>
            <h1>${new Date().toLocaleString("default", { dateStyle: "full" })}</h1>
            ${windows.map(renderWindow).join(`
`)}
        </blockquote></body></html>`;
  }

  /**
   * Exports the current window's tabs in the specified format and triggers a download.
   * @param {"json"|"csv"|"markdown"|"html"} format
   * @returns {Promise<void>}
   */
  async export(format) {
    try {
      // Get the current window and its tabs
      const windowData = await chrome.windows.get(globalThis.windowId || chrome.windows.WINDOW_ID_CURRENT, {
        windowTypes: ["normal"],
        populate: true,
      });

      // Get window titles for HTML export if needed
      if (format === "html") {
        this.winTitles = await getWindowTitles();
      }

      // Generate content based on format
      let content, mimeType, extension;
      switch (format) {
        case "json":
          content = this.toJSON(windowData);
          mimeType = "application/json";
          extension = "json";
          break;
        case "csv":
          content = this.toCSV(windowData);
          mimeType = "text/csv";
          extension = "csv";
          break;
        case "markdown":
          content = this.toMarkdown(windowData);
          mimeType = "text/markdown";
          extension = "md";
          break;
        case "html":
          content = this.toHTML([windowData]);
          mimeType = "text/html";
          extension = "html";
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      // Create and trigger download
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const filename = `tabs-${new Date().toISOString().split('T')[0]}.${extension}`;
      
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      a.click();
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Export failed:", error);
      throw error;
    }
  }

  /**
   * Imports tabs from a JSON file and opens them in the current window.
   * @param {string} jsonContent - The JSON content containing tab data
   * @returns {Promise<void>}
   */
  async import(jsonContent) {
    try {
      const tabs = JSON.parse(jsonContent);
      if (!Array.isArray(tabs)) {
        throw new Error("Invalid JSON format: expected an array of tabs");
      }

      // Open each tab
      for (const tab of tabs) {
        if (tab.url) {
          await createTab({ url: tab.url, active: false });
        }
      }
    } catch (error) {
      console.error("Import failed:", error);
      throw error;
    }
  }
}

export { TabExportUtility };
