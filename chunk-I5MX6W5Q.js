/**
 * @fileoverview
 * Defines the ReportBugDialog custom element, which displays an error dialog and allows users to report bugs via Gmail.
 * This implementation is fully deobfuscated, documented, and production-ready.
 */

/**
 * Custom dialog element for displaying errors and reporting bugs via Gmail.
 * 
 * @extends HTMLDialogElement
 */
class ReportBugDialog extends HTMLDialogElement {
  /**
   * The error object to display and report.
   * @type {Error}
   */
  error;

  /**
   * The base Gmail compose URL.
   * @type {string}
   */
  gmailComposeUrl = "https://mail.google.com/mail/u/0/";

  /**
   * The support email address.
   * @type {string}
   */
  supportEmail = "<EMAIL>";

  /**
   * @param {Error} error - The error to display and report.
   */
  constructor(error) {
    super();
    this.error = error;
  }

  /**
   * Opens a new Gmail tab with a pre-filled bug report containing error details and extension info.
   * @returns {Promise<void>}
   */
  async reportBug() {
    // Get extension manifest details
    const { version, short_name } = chrome.runtime.getManifest();

    // Prepare stack trace for URL encoding
    const encodedStack = this.error.stack?.replaceAll('\n', '%0A');

    // Compose the Gmail URL with all relevant details
    const mailUrl = `${this.gmailComposeUrl}?to=${this.supportEmail}` +
      `&su=${encodeURIComponent(this.error.message)}` +
      `&body=Error:%20${encodeURIComponent(this.error.message)}` +
      `%0AStack:%20${encodedStack}` +
      `%0AExtensionId:%20${chrome.runtime.id}` +
      `%0AExtension%20Name:%20${encodeURIComponent(short_name)}` +
      `%0AExtension%20Version:%20${encodeURIComponent(version)}` +
      `%0ABrowser%20Info:%20${encodeURIComponent(navigator.userAgent)}` +
      `&fs=1&tf=cm`;

    // Open the Gmail compose window in a new tab
    await chrome.tabs.create({ url: mailUrl });
  }

  /**
   * Renders the dialog's HTML content.
   * @returns {string} The HTML string for the dialog.
   */
  render() {
    // Escape angle brackets in the error message for safe HTML display
    const safeMessage = this.error.message
      ?.replaceAll('<', '<')
      ?.replaceAll('>', '>');

    return `
      <h2>😪 ${chrome.i18n.getMessage("sorry_something_went_wrong")} 🐛</h2>
      <p style="max-width: 50ch;overflow-wrap: break-word;">
        ❌ ${safeMessage}
      </p>
      <button style="font-size: 1rem;margin-inline: auto;display: block;">
        🐞 <span>${chrome.i18n.getMessage("report_bug")}</span>
      </button>
      <div style="text-align:center;font-size:x-small;">
        ${chrome.i18n.getMessage("just_one_click")}
      </div>
    `;
  }

  /**
   * Lifecycle callback: Invoked when the element is added to the DOM.
   * Sets up the dialog content and event listeners.
   */
  connectedCallback() {
    this.id = "report-bug";
    this.innerHTML = this.render();
    this.showModal();

    // The report bug button is the second-to-last child (before the info div)
    const reportButton = this.lastElementChild.previousElementSibling;
    reportButton.addEventListener("click", this.reportBug.bind(this));
  }
}

// Register the custom element, extending <dialog>
customElements.define("report-bug", ReportBugDialog, { extends: "dialog" });

// Export the class for use elsewhere
export { ReportBugDialog as ReportBugDialog };
