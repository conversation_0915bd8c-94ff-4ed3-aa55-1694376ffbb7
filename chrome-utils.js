/**
 * Chrome Extension Utilities and Reactive DOM Helpers
 * Consolidated Chrome API bindings, color constants, DOM utilities,
 * and reactive data binding system for TabVertikal extension.
 *
 * @module ChromeUtils
 */

// --- Chrome API Bindings ---

/** Query all tabs. */
export const queryTabs = chrome.tabs.query.bind(chrome.tabs);
/** Get a tab by ID. */
export const getTab = chrome.tabs.get.bind(chrome.tabs);
/** Create a new tab. */
export const createTab = chrome.tabs.create.bind(chrome.tabs);
/** Remove a tab by ID. */
export const removeTab = chrome.tabs.remove.bind(chrome.tabs);
/** Remove multiple tabs by ID array. */
export const removeTabs = chrome.tabs.remove.bind(chrome.tabs);
/** Update a tab. */
export const updateTab = chrome.tabs.update.bind(chrome.tabs);
/** Move tabs. */
export const moveTabs = chrome.tabs.move.bind(chrome.tabs);
/** Group tabs. */
export const groupTabs = chrome.tabs.group.bind(chrome.tabs);
/** Ungroup tabs. */
export const ungroupTabs = chrome.tabs.ungroup.bind(chrome.tabs);

/** Query all tab groups. */
export const queryTabGroups = chrome.tabGroups.query.bind(chrome.tabGroups);
/** Update tab group. */
export const updateTabGroup = chrome.tabGroups.update.bind(chrome.tabGroups);
/** Move tab groups. */
export const moveTabGroups = chrome.tabGroups.move.bind(chrome.tabGroups);

/** Query all windows. */
export const queryWindows = chrome.windows.query.bind(chrome.windows);
/** Get window by ID. */
export const getWindow = chrome.windows.get.bind(chrome.windows);
/** Create new window. */
export const createWindow = chrome.windows.create.bind(chrome.windows);
/** Update window. */
export const updateWindow = chrome.windows.update.bind(chrome.windows);

/** Get item from localStorage. */
export const getLocalStorageItem = localStorage.getItem.bind(localStorage);
/** Set item in localStorage. */
export const setLocalStorageItem = localStorage.setItem.bind(localStorage);

/** Get items from chrome.storage.sync. */
export const getSyncStorage = chrome.storage.sync.get.bind(chrome.storage.sync);
/** Set items in chrome.storage.sync. */
export const setSyncStorage = chrome.storage.sync.set.bind(chrome.storage.sync);

/** Get items from chrome.storage.session. */
export const getSessionStorage = chrome.storage.session.get.bind(chrome.storage.session);
/** Set items in chrome.storage.session. */
export const setSessionStorage = chrome.storage.session.set.bind(chrome.storage.session);

/** Get item from sessionStorage. */
export const getSessionItem = sessionStorage.getItem.bind(sessionStorage);
/** Set item in sessionStorage. */
export const setSessionItem = sessionStorage.setItem.bind(sessionStorage);

// --- Color Constants ---

/** List of supported tab group colors. */
export const TAB_GROUP_COLORS = [
  "blue", "red", "yellow", "green", "cyan", "purple", "pink", "orange", "grey"
];

/** Mapping of color names to RGB values. */
export const TAB_GROUP_COLOR_RGB = {
  blue: "0, 0, 255",
  red: "255, 0, 0",
  yellow: "255, 251, 0",
  green: "0, 128, 0",
  cyan: "0, 255, 255",
  purple: "128, 0, 128",
  pink: "255, 192, 203",
  orange: "255, 165, 0",
  grey: "128, 128, 128",
};

/** Base URL for extension themes. */
export const THEME_BASE_URL = "chrome-extension://";

// --- Utility Functions ---

/**
 * Get window titles for all windows.
 * @returns {Promise<string[]>} Array of window titles
 */
export async function getWindowTitles() {
  const windows = await queryWindows({ populate: true });
  return windows.map(win => win.title || `Window ${win.id}`);
}

/**
 * Create a tab in a specific window.
 * @param {Object} options - Tab creation options
 * @param {number} options.windowId - Window ID to create tab in
 * @param {string} options.url - URL for the new tab
 * @param {boolean} [options.active] - Whether tab should be active
 * @returns {Promise<chrome.tabs.Tab>} Created tab
 */
export async function createTabInWindow(options) {
  return createTab(options);
}

/**
 * Fetch all tab groups across all windows.
 * @returns {Promise<chrome.tabGroups.TabGroup[]>} Array of tab groups
 */
export async function fetchTabGroups() {
  return queryTabGroups({});
}

// --- DOM Utilities ---

/**
 * Create a reactive proxy for an object that automatically updates DOM when properties change.
 * @param {Object} obj - Object to make reactive
 * @param {Function} updateCallback - Function to call when object changes
 * @returns {Proxy} Reactive proxy object
 */
export function makeReactive(obj, updateCallback) {
  return new Proxy(obj, {
    set(target, property, value) {
      const oldValue = target[property];
      target[property] = value;
      if (oldValue !== value && updateCallback) {
        updateCallback(property, value, oldValue);
      }
      return true;
    },
    get(target, property) {
      return target[property];
    }
  });
}

/**
 * Template literal tag function for creating HTML with reactive data binding.
 * Processes template strings and binds reactive data to DOM elements.
 * 
 * @param {TemplateStringsArray} strings - Template string parts
 * @param {...any} values - Template interpolated values
 * @returns {DocumentFragment} DOM fragment with bound reactive data
 */
export function html(strings, ...values) {
  // Combine template strings with values
  let htmlString = strings[0];
  for (let i = 0; i < values.length; i++) {
    htmlString += String(values[i]) + strings[i + 1];
  }

  // Create template element and extract fragment
  const template = document.createElement('template');
  template.innerHTML = htmlString.trim();
  return document.importNode(template.content, true);
}

/**
 * Bind a reactive array to a DOM fragment, automatically updating when array changes.
 * @param {Array} array - Reactive array to bind
 * @param {Function} itemRenderer - Function that renders each array item to DOM
 * @param {DocumentFragment} container - Container to render items into
 * @returns {Function} Cleanup function to remove bindings
 */
export function bindReactiveArray(array, itemRenderer, container) {
  const renderedItems = new Map();
  
  function updateContainer() {
    // Clear container
    while (container.firstChild) {
      container.removeChild(container.firstChild);
    }
    renderedItems.clear();
    
    // Render all items
    array.forEach((item, index) => {
      const element = itemRenderer(item, index);
      renderedItems.set(item, element);
      container.appendChild(element);
    });
  }

  // Initial render
  updateContainer();

  // Return cleanup function
  return () => {
    renderedItems.clear();
  };
}

// --- Extension-specific Utilities ---

/**
 * Setup global utilities with i18n context.
 * @param {Function} setupCallback - Callback to setup global utilities
 * @returns {Function} Setup function that accepts i18n context
 */
export function withGlobalSetup(setupCallback) {
  return function(i18nContext) {
    return setupCallback(i18nContext);
  };
}

/**
 * Setup i18n context and utilities.
 * @param {Function} callback - Callback to setup with i18n
 * @returns {Function} Setup function
 */
export function withI18n(callback) {
  return function() {
    // Basic i18n setup - extend as needed
    const i18nContext = {
      t: (key, defaultValue) => chrome.i18n.getMessage(key) || defaultValue || key
    };
    return callback(i18nContext);
  };
}

/**
 * Check if a URL matches auto-group rules.
 * @param {string} url - URL to check
 * @returns {Promise<Object|null>} Matching rule or null
 */
export async function checkAutoGroupRule(url) {
  const { autoGroupRules = [] } = await getSyncStorage(['autoGroupRules']);
  return autoGroupRules.find(rule => {
    if (rule.pattern && url.includes(rule.pattern)) {
      return true;
    }
    if (rule.regex) {
      try {
        const regex = new RegExp(rule.regex);
        return regex.test(url);
      } catch (e) {
        console.warn('Invalid regex in auto-group rule:', rule.regex);
      }
    }
    return false;
  }) || null;
}

/**
 * Set a setting in sync storage.
 * @param {string} key - Setting key
 * @param {any} value - Setting value
 * @returns {Promise<void>}
 */
export async function setSetting(key, value) {
  return setSyncStorage({ [key]: value });
}

/**
 * Get a setting from sync storage.
 * @param {string} key - Setting key
 * @param {any} defaultValue - Default value if not found
 * @returns {Promise<any>} Setting value
 */
export async function getSetting(key, defaultValue = null) {
  const result = await getSyncStorage([key]);
  return result[key] !== undefined ? result[key] : defaultValue;
}

// --- Aliased Exports for Compatibility ---
// These aliases maintain compatibility with existing obfuscated imports

export { fetchTabGroups as e };
export { getSyncStorage as g };
export { setSyncStorage as h };
export { getSessionStorage as i };
export { setSessionStorage as j };
export { TAB_GROUP_COLORS as k };
export { TAB_GROUP_COLOR_RGB as l };
export { THEME_BASE_URL as m };
export { makeReactive as n };
export { html as o };
export { bindReactiveArray as p };

// Additional aliases
export { moveTabGroups as moveTabGroups };
export { getSessionStorage as getSessionStorageItem };
export { setSessionStorage as setSessionStorageItem };
export { getSyncStorage as getSyncStorageItem };
export { setSyncStorage as setSyncStorageItem };
export { TAB_GROUP_COLOR_RGB as COLOR_RGB_MAP };
export { bindReactiveArray as bindArrayToFragment };
export { html as renderTemplate };
export { bindReactiveArray as mapTemplate };
