import { a as ErrorDialog } from "./chunk-I5MX6W5Q.js";
import {
  a as fetchTabData,
  e as findTabGroupsByTitle,
  k as availableColors,
  n as cloneRule,
  o as html,
  p as mapList,
} from "./chrome-utils.js";
import "./chunk-EK7ODJWE.js";

/**
 * Represents a rule for grouping browser tabs.
 */
class TabGroupRule {
  /**
   * @param {Object} urlMatches - URL match patterns.
   * @param {Array<string>} titleIncludes - Titles to include.
   * @param {string} color - Group color.
   * @param {string} name - Rule name.
   * @param {string} ctmGroupTitle - Custom group title.
   */
  constructor(urlMatches, titleIncludes, color, name = "", ctmGroupTitle = "") {
    this.id = "r" + Math.random().toString(36).slice(2);
    this.name = name;
    this.ctmGroupTitle = ctmGroupTitle;
    this.color = color ?? "grey";
    this.urlMatches = urlMatches ?? {};
    this.titleIncludes = titleIncludes ?? [];
    this.priority = 0;
    this.enabled = true;
    this.createdAt = Date.now();
    this.lastModifiedAt = Date.now();
  }
}

// IndexedDB store keys
const TAB_GROUP_RULES_STORE = { TabGroupRules: "TabGroupRules" };

/**
 * IndexedDB upgrade handler.
 * @param {IDBVersionChangeEvent} event
 */
function handleDbUpgrade({ target }) {
  target.result.createObjectStore(TAB_GROUP_RULES_STORE.TabGroupRules, { keyPath: "id" });
}

/**
 * Opens the IndexedDB for tab group rules.
 * @returns {Promise<IDBDatabase>}
 */
function openTabGroupRulesDb() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open("tabVertikal", 1);
    request.onupgradeneeded = handleDbUpgrade;
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
    request.onblocked = () => console.warn("Pending until unblocked");
  });
}

/**
 * Retrieves all tab group rules from the database.
 * @returns {Promise<TabGroupRule[]>}
 */
async function getAllTabGroupRules() {
  return new Promise((resolve, reject) => {
    openTabGroupRulesDb().then((db) => {
      const transaction = db
        .transaction(TAB_GROUP_RULES_STORE.TabGroupRules, "readonly")
        .objectStore(TAB_GROUP_RULES_STORE.TabGroupRules)
        .getAll();
      transaction.onsuccess = ({ target }) => resolve(target.result);
      transaction.onerror = (event) => reject(event);
      db.close();
    });
  });
}

/**
 * Saves a tab group rule to the database.
 * @param {TabGroupRule} rule
 * @returns {Promise<TabGroupRule>}
 */
async function saveTabGroupRule(rule) {
  return new Promise((resolve, reject) => {
    openTabGroupRulesDb().then((db) => {
      const request = db
        .transaction(TAB_GROUP_RULES_STORE.TabGroupRules, "readwrite")
        .objectStore(TAB_GROUP_RULES_STORE.TabGroupRules)
        .put(rule);
      request.onsuccess = () => resolve(rule);
      request.onerror = (event) => reject(event);
      db.close();
    });
  });
}

/**
 * Updates a property of a tab group rule.
 * @param {string} ruleId
 * @param {string} property
 * @param {*} value
 * @returns {Promise<TabGroupRule>}
 */
async function updateTabGroupRuleProperty(ruleId, property, value) {
  return new Promise((resolve, reject) => {
    openTabGroupRulesDb().then((db) => {
      const store = db
        .transaction(TAB_GROUP_RULES_STORE.TabGroupRules, "readwrite")
        .objectStore(TAB_GROUP_RULES_STORE.TabGroupRules);
      const getRequest = store.get(ruleId);
      getRequest.onsuccess = ({ target }) => {
        const rule = target.result;
        rule[property] = value;
        rule.lastModifiedAt = Date.now();
        const putRequest = store.put(rule);
        putRequest.onsuccess = () => resolve(rule);
        putRequest.onerror = (event) => reject(event);
      };
      getRequest.onerror = (event) => reject(event);
      db.close();
    });
  });
}

/**
 * Deletes a tab group rule from the database.
 * @param {string} ruleId
 * @returns {Promise<void>}
 */
async function deleteTabGroupRule(ruleId) {
  return new Promise((resolve, reject) => {
    openTabGroupRulesDb().then((db) => {
      const request = db
        .transaction(TAB_GROUP_RULES_STORE.TabGroupRules, "readwrite")
        .objectStore(TAB_GROUP_RULES_STORE.TabGroupRules)
        .delete(ruleId);
      request.onsuccess = ({ target }) => resolve(target.result);
      request.onerror = (event) => reject(event);
      db.close();
    });
  });
}

/**
 * Utility for extracting tab URL and title data.
 */
class TabUrlTitleContext {
  constructor() {}
  /**
   * Fetches tab URLs and titles.
   * @returns {Promise<void>}
   */
  async getUrls() {
    const tabData = await fetchTabData({});
    this.tabUrls = tabData.map((tab) => new URL(tab.url));
    this.titles = tabData.map((tab) => tab.title);
  }
  /**
   * Gets top-level domains from tab URLs.
   * @returns {string[]}
   */
  topDomains() {
    return this.tabUrls.map(
      (url) => url.host.slice(url.host.lastIndexOf(".", url.host.lastIndexOf(".") - 1) + 1)
    );
  }
  /**
   * Gets domains from tab URLs.
   * @returns {string[]}
   */
  domains() {
    return this.tabUrls.map((url) => url.host);
  }
  /**
   * Gets first path segments from tab URLs.
   * @returns {string[]}
   */
  firstPathSegments() {
    return this.tabUrls.map((url) =>
      url.pathname.slice(1, url.pathname.indexOf("/", 1))
    );
  }
  /**
   * Gets first two path segments from tab URLs.
   * @returns {string[]}
   */
  twoPathSegments() {
    return this.tabUrls.map((url) =>
      url.pathname.slice(
        1,
        url.pathname.indexOf("/", url.pathname.indexOf("/", 1) + 1)
      )
    );
  }
  /**
   * Gets significant words from tab titles.
   * @returns {string[]}
   */
  titleWords() {
    return this.titles.flatMap((title) =>
      title.split(" ").filter((word) => word.length > 3)
    );
  }
}

/**
 * Custom element for displaying and editing a tab group rule.
 */
class TabGroupRuleElement extends HTMLElement {
  /**
   * @param {TabGroupRule} groupRule
   */
  constructor(groupRule) {
    super();
    this.groupRule = groupRule;
  }

  /**
   * Opens the rule editor dialog.
   */
  editGroupRule() {
    const dialog = new CreateRuleDialog(this.groupRule);
    this.parentElement.parentElement.appendChild(dialog);
  }

  /**
   * Deletes the group rule.
   * @returns {Promise<void>}
   */
  async deleteGroupRule() {
    try {
      await deleteTabGroupRule(this.groupRule.id);
      this.remove();
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * Property update handlers for the rule.
   */
  updateRuleProp = {
    name: ({ target }) => {
      updateTabGroupRuleProperty(this.groupRule.id, "name", target.value);
      this.groupRule.name = target.value;
    },
    ctmGroupName: async ({ target }) => {
      updateTabGroupRuleProperty(this.groupRule.id, "ctmGroupName", target.value);
      const tabGroups = await findTabGroupsByTitle({ title: this.groupRule.ctmGroupTitle });
      this.groupRule.ctmGroupTitle = target.value;
      if (tabGroups.length !== 0)
        chrome.tabGroups.update(tabGroups[0].id, { title: target.value });
    },
    color: async ({ target }) => {
      updateTabGroupRuleProperty(this.groupRule.id, "color", target.value);
      const tabGroups = await findTabGroupsByTitle({ title: this.groupRule.ctmGroupTitle });
      this.groupRule.color = target.value;
      if (tabGroups.length !== 0)
        chrome.tabGroups.update(tabGroups[0].id, { color: target.value });
    },
  };

  /**
   * Toggles the enabled state of the rule.
   * @param {Event} event
   */
  toggleGroupRule({ target }) {
    updateTabGroupRuleProperty(this.groupRule.id, "enabled", target.value);
  }

  /**
   * Renders the rule element.
   * @returns {TemplateResult}
   */
  render() {
    const matchesSummary = (() => {
      const keys = [...Object.keys(this.groupRule.urlMatches)];
      this.groupRule.titleIncludes.length === 0 || keys.push("title");
      return keys.join(",");
    })();
    return html`<input type="checkbox" name="" class="toggle_rule" @change=${this.toggleGroupRule.bind(this)} />
      <rule-details>
        <div class="left-column">
          <label>
            <span>${i18n("name")}:</span>
            <input type="text" value=${this.groupRule.name} @change=${this.updateRuleProp.name} />
          </label>
          <label>
            <span>${i18n("group")}:</span>
            <input
              type="text"
              value="${this.groupRule.ctmGroupTitle ?? "auto"}"
              @change=${this.updateRuleProp.ctmGroupName} />
          </label>
          <label>
            <span>${i18n("priority")}:</span>
            <input type="number" value=${this.groupRule.priority} @change=${this.updateRuleProp.priority} />
          </label>
        </div>
        <div class="center-column"></div>
        <div class="right-column">
          <div>
            <span>${i18n("matches")}:</span>
            <var title="${matchesSummary}">${matchesSummary.slice(0, 10)}</var>
          </div>
          <label>
            <span>${i18n("color")}:</span>
            <select id="color_select" value=${this.groupRule.color} @change=${this.updateRuleProp.color}>
              <option value="blue">blue</option>
              <option value="red">red</option>
              <option value="yellow">yellow</option>
              <option value="green">green</option>
              <option value="cyan">cyan</option>
              <option value="purple">purple</option>
              <option value="pink">pink</option>
              <option value="orange">orange</option>
              <option value="grey">grey</option>
            </select>
          </label>
          <div style="justify-content: end">
            <vt-icon
              ico="edit"
              title="${i18n("edit_rule")}"
              class="edit-icon"
              @click=${this.editGroupRule.bind(this)}></vt-icon>
            <vt-icon
              ico="delete"
              title="${i18n("delete_rule")}"
              class="delete-icon"
              @click=${this.deleteGroupRule.bind(this)}></vt-icon>
          </div>
        </div>
      </rule-details>`;
  }

  /**
   * Called when the element is added to the DOM.
   */
  connectedCallback() {
    this.id = this.groupRule.id;
    this.replaceChildren(this.render());
    $("#color_select", this).value = this.groupRule.color;
  }
}
customElements.define("tabgroup-rule", TabGroupRuleElement);

/**
 * Renders or updates a tab group rule element in the DOM.
 * @param {TabGroupRule} rule
 */
async function renderOrUpdateTabGroupRule(rule) {
  const ruleElement = new TabGroupRuleElement(rule);
  const existing = $("#r" + rule.id, tabGroupRuleListElement);
  if (existing) {
    existing.replaceWith(ruleElement);
  } else {
    tabGroupRuleListElement.appendChild(ruleElement);
  }
  const store = await getStore("autoGroupingOn");
  if (!store.autoGroupingOn) {
    chrome.runtime.sendMessage({
      msg: "toggle_auto_grouping",
      autoGroupOn: true,
    });
  }
}

// Global reference for the rule list element
let tabGroupRuleListElement;

/**
 * Custom element for the list of tab group rules.
 */
class TabGroupRuleListElement extends HTMLElement {
  constructor() {
    super();
    tabGroupRuleListElement = this;
  }
  /**
   * Renders the list of rules.
   * @param {TabGroupRule[]} rules
   * @returns {TabGroupRuleElement[]}
   */
  render(rules) {
    return rules.map((rule) => new TabGroupRuleElement(rule));
  }
  /**
   * Called when the element is added to the DOM.
   */
  async connectedCallback() {
    const rules = await getAllTabGroupRules();
    this.replaceChildren(...this.render(rules));
  }
}
customElements.define("tabgroup-rule-list", TabGroupRuleListElement);

/**
 * Dialog element for creating or editing a tab group rule.
 */
class CreateRuleDialog extends HTMLDialogElement {
  /**
   * @param {TabGroupRule} rule
   */
  constructor(rule) {
    super();
    rule ??= new TabGroupRule();
    this.rule = cloneRule(rule);
  }

  /**
   * Sets input values based on the rule.
   */
  setInputValue() {
    for (const matchKey in this.rule.urlMatches) {
      $(`input[value="${matchKey}"]`, this).checked = true;
      $(`input[name="${matchKey}"]`, this).value = this.rule.urlMatches[matchKey].join(",");
    }
    $(`input[value="${this.rule.color}"]`, this).checked = true;
  }

  /**
   * Creates a new rule and saves it.
   * @returns {Promise<void>}
   */
  async createRule() {
    const ruleCopy = Object.assign({}, this.rule);
    ruleCopy.urlMatches = {};
    for (const key in this.rule.urlMatches) {
      ruleCopy.urlMatches[key] = Object.assign([], this.rule.urlMatches[key]);
    }
    ruleCopy.titleIncludes = Object.assign([], this.rule.titleIncludes);
    try {
      await saveTabGroupRule(ruleCopy);
      renderOrUpdateTabGroupRule(structuredClone(ruleCopy));
      this.remove();
    } catch (error) {
      console.error(error);
      document.body.appendChild(new ErrorDialog(error));
    }
  }

  /**
   * Removes a title match from the rule.
   * @param {Event} param0
   */
  removeMatchTitle({ currentTarget: currentTargetElement, target: targetElement }) {
    const title = targetElement.closest("li").textContent.trim();
    const index = this.rule.titleIncludes.indexOf(title);
    if (index !== -1) this.rule.titleIncludes.splice(index, 1);
    if (!targetElement.closest("vt-icon")) {
      currentTargetElement.previousElementSibling.previousElementSibling.value = title;
    }
  }

  /**
   * Adds a title match to the rule.
   * @param {KeyboardEvent} param0
   */
  addMatchTitle({ code: keyCode, target: inputElement }) {
    if (keyCode === "Enter") {
      this.rule.titleIncludes.push(inputElement.value);
      inputElement.value = "";
    }
  }

  /**
   * Handles changes to URL match inputs.
   * @param {Event} event
   */
  onUrlMatchChange(event) {
    const inputElement = event.target;
    if (inputElement.type === "checkbox") {
      const handleCheckbox = (checkbox) => {
        checkbox.disabled = inputElement.checked;
        if (inputElement.checked) {
          delete this.rule.urlMatches[checkbox.value];
        } else if (checkbox.checked) {
          this.rule.urlMatches[checkbox.value] =
            checkbox.parentElement.nextElementSibling.value.trim();
        }
      };
      if (
        inputElement.value === "two_path_segment" ||
        inputElement.value === "domain"
      ) {
        const relatedCheckbox =
          event.target.closest("li").previousElementSibling.firstElementChild
            .firstElementChild;
        handleCheckbox(relatedCheckbox);
      }
      if (inputElement.value === "ctm_match_pattern") {
        for (const checkbox of event.currentTarget.querySelectorAll(
          'input[name="url-match"]'
        )) {
          if (checkbox !== inputElement) handleCheckbox(checkbox);
        }
      }
      if (inputElement.checked) {
        const value = event.target.parentElement.nextElementSibling.value.trim();
        this.rule.urlMatches[inputElement.value] = value ? value.split(",") : [];
      } else {
        delete this.rule.urlMatches[inputElement.value];
      }
    } else if (
      inputElement.previousElementSibling.firstElementChild.checked
    ) {
      this.rule.urlMatches[event.target.name] = inputElement.value.trim()
        ? inputElement.value.split(",")
        : [];
    }
  }

  /**
   * Handles color selection changes.
   * @param {Event} param0
   */
  onColorChange({ target: colorInput }) {
    this.rule.color = colorInput.value;
  }

  /**
   * Renders the dialog content.
   * @param {TabUrlTitleContext} context
   * @returns {TemplateResult}
   */
  render(context) {
    const renderChip = (title) =>
      html`<li class="chip-item"><span>${title}</span><vt-icon ico="close" title="remove"></vt-icon></li>`;
    const renderColorOption = (color) =>
      html`<label style="--clr:${color}">
        <input type="radio" name="group-color" value="${color}" hidden />
      </label>`;
    const renderDatalistOption = (option) =>
      html`<option value="${option}"></option>`;
    return html`<vt-icon ico="close-circle" class="close-btn" @click=${this.remove.bind(this)}></vt-icon>
      <label>
        <span>${i18n("rule_name")}</span>
        <input type="text" .value=${() => this.rule.name} />
      </label>

      <section class="title-match" style="margin-top:0.8em">
        <label>
          <span><vt-icon ico="title"></vt-icon> ${i18n("tab_title_includes")}</span>
          <input type="text" list="tabTitles" @keyup=${this.addMatchTitle.bind(this)} />
        </label>
        <datalist id="tabTitles">${context.titleWords().map(renderDatalistOption)}</datalist>
        <ul class="chip-list" @click=${this.removeMatchTitle.bind(this)}>
          ${mapList(this.rule.titleIncludes, renderChip)}
        </ul>
      </section>
      <section class="url-match">
        <header><vt-icon ico="route"></vt-icon> <span>${i18n("url_match")}</span></header>
        <ul @change=${this.onUrlMatchChange.bind(this)}>
          <li>
            <label> <input type="checkbox" name="url-match" value="top_domain" />${i18n("top_domain")}</label>
            <input
              type="email"
              name="top_domain"
              list="top_domains"
              placeholder="${i18n("enter_selected_top_domains")}" />
            <datalist id="top_domains">${context.topDomains().map(renderDatalistOption)}</datalist>
          </li>
          <li>
            <label><input type="checkbox" name="url-match" value="domain" />${i18n("domain")} </label>
            <input type="email" name="domain" list="domains" placeholder="${i18n("enter_selected_domains")}" />
            <datalist id="domains">${context.domains().map(renderDatalistOption)}</datalist>
          </li>
          <li>
            <label>
              <input type="checkbox" name="url-match" value="first_path_segment" /> ${i18n("first_path_segment")}
            </label>
            <input
              type="email"
              name="first_path_segment"
              list="first_path_segments"
              placeholder="${i18n("selected_first_path")}" />
            <datalist id="first_path_segments">${context.firstPathSegments().map(renderDatalistOption)}</datalist>
          </li>
          <li>
            <label>
              <input type="checkbox" name="url-match" value="two_path_segment" /> ${i18n("two_path_segment")}
            </label>
            <input
              type="email"
              name="two_path_segment"
              list="two_path_segments"
              placeholder="${i18n("selected_first_two_path")}" />
            <datalist id="two-path-segments">${context.twoPathSegments().map(renderDatalistOption)}</datalist>
          </li>
          <li>
            <label>
              <input type="checkbox" name="url-match" value="ctm_match_pattern" />
              <span>
                ${i18n("custom_url")}
                <a href="https://developer.chrome.com/docs/extensions/develop/concepts/match-patterns">
                  ${i18n("match_patterns")}
                </a>
              </span>
            </label>
            <input type="email" name="ctm_match_pattern" placeholder="https://*/foo*,http://google.es/*" />
          </li>
        </ul>
      </section>

      <section class="rule-row">
        <label>
          <span>${i18n("custom_tabgroup_title")}</span>
          <input
            type="text"
            .value=${() => this.rule.ctmGroupTitle}
            placeholder="(optional) custom title"
            style="width:80%" />
        </label>
        <label>
          <span>${i18n("priority")}</span>
          <input type="number" .value=${() => this.rule.priority} />
        </label>
      </section>

      <section>
        <span style="margin-bottom:0.4em"><vt-icon ico="color"></vt-icon> ${i18n("color")}</span>
        <div class="color-pot" @change=${this.onColorChange.bind(this)}>${availableColors.map(renderColorOption)}</div>
      </section>
      <button @click=${this.createRule.bind(this)}>${i18n("create_rule")}</button>`;
  }

  /**
   * Called when the dialog is added to the DOM.
   */
  async connectedCallback() {
    this.id = "create-rule-dialog";
    const context = new TabUrlTitleContext();
    await context.getUrls();
    this.replaceChildren(this.render(context));
    this.showModal();
    this.setInputValue();
  }
}
customElements.define("create-rule-dialog", CreateRuleDialog, { extends: "dialog" });

import tabGroupRulesStyles from "./tabgroup-rules-GCDCIF3A.css" with { type: "css" };

/**
 * Dialog element for managing tab group rules.
 */
class TabGroupRuleDialog extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [tabGroupRulesStyles];
  }
  /**
   * Shows the rule creator dialog.
   */
  showRuleCreator() {
    const dialog = new CreateRuleDialog();
    this.shadowRoot.firstElementChild.appendChild(dialog);
  }
  /**
   * Renders the dialog content.
   * @returns {Array}
   */
  render() {
    return [
      html`<header>
        <vt-icon ico="close-circle" class="close-btn" @click=${this.remove.bind(this)}></vt-icon>
        <span><vt-icon ico="group-rule"></vt-icon> ${i18n("tabgroup_rules")}</span>
        <button @click=${this.showRuleCreator.bind(this)}><vt-icon ico="plus"></vt-icon> ${i18n("add_rule")}</button>
      </header>`,
      new TabGroupRuleListElement(),
    ];
  }
  /**
   * Called when the dialog is added to the DOM.
   */
  connectedCallback() {
    const dialog = document.createElement("dialog");
    dialog.append(...this.render());
    this.shadowRoot.appendChild(dialog);
    dialog.showModal();
  }
}
customElements.define("tabgroup-rule-dialog", TabGroupRuleDialog);

export { TabGroupRuleDialog as TabGroupRuleDialog };
