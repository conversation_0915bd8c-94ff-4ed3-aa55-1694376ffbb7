// Utility functions for module interop and property management

/**
 * Creates a new object with the prototype of the given object.
 * @type {typeof Object.create}
 */
const createObject = Object.create;

/**
 * Defines a property directly on an object.
 * @type {typeof Object.defineProperty}
 */
const defineProperty = Object.defineProperty;

/**
 * Retrieves the descriptor for a property on an object.
 * @type {typeof Object.getOwnPropertyDescriptor}
 */
const getPropertyDescriptor = Object.getOwnPropertyDescriptor;

/**
 * Returns an array of all properties (enumerable or not) found directly upon a given object.
 * @type {typeof Object.getOwnPropertyNames}
 */
const getOwnPropertyNames = Object.getOwnPropertyNames;

/**
 * Returns the prototype of the specified object.
 * @type {typeof Object.getPrototypeOf}
 */
const getPrototypeOf = Object.getPrototypeOf;

/**
 * Checks if an object has the specified property as its own property.
 * @type {typeof Object.prototype.hasOwnProperty}
 */
const hasOwnProperty = Object.prototype.hasOwnProperty;

/**
 * Wraps a module factory function to create a CommonJS-like module with caching.
 * @param {Function} moduleFactory - The module factory function.
 * @returns {Function} - A function that returns the module's exports.
 */
const createCjsModule = (moduleFactory) => {
  let moduleCache;
  return () => {
    if (!moduleCache) {
      const module = { exports: {} };
      moduleFactory(module.exports, module);
      moduleCache = module.exports;
    }
    return moduleCache;
  };
};

/**
 * Copies properties from the source object to the target object, excluding a specific property.
 * Preserves property descriptors and avoids overwriting existing properties.
 * @param {Object} target - The target object to copy properties to.
 * @param {Object} source - The source object to copy properties from.
 * @param {string} [excludeProperty] - A property name to exclude from copying.
 * @returns {Object} - The target object with copied properties.
 */
const copyProperties = (target, source, excludeProperty) => {
  if ((source && typeof source === "object") || typeof source === "function") {
    for (const key of getOwnPropertyNames(source)) {
      if (
        !hasOwnProperty.call(target, key) &&
        key !== excludeProperty
      ) {
        const descriptor = getPropertyDescriptor(source, key);
        defineProperty(target, key, {
          get: () => source[key],
          enumerable: !descriptor || descriptor.enumerable,
        });
      }
    }
  }
  return target;
};

/**
 * Converts a CommonJS module to an ES module-like object, preserving the default export.
 * @param {Object} module - The CommonJS module object.
 * @param {Object} [source] - The source object to copy properties from.
 * @returns {Object} - An ES module-like object.
 */
const toEsModule = (module, source) => {
  // Create a new object with the prototype of the module, or a plain object if module is null/undefined
  const esModule = module != null ? createObject(getPrototypeOf(module)) : {};
  // If source is provided or module is not an ES module, define a default export
  const result =
    source || !module || !module.__esModule
      ? defineProperty(esModule, "default", { value: module, enumerable: true })
      : esModule;
  // Copy all properties from module to result, excluding "default"
  return copyProperties(result, module);
};

export {
  createCjsModule as createCjsModule,
  toEsModule as toEsModule,
};
