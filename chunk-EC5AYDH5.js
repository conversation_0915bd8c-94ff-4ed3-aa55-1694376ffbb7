/**
 * Window Management Components for TabVertikal
 * Provides UI and logic for window switching, renaming, and moving tabs/groups between windows.
 * 
 * @module WindowManagement
 */

import { 
  f as moveTabGroup, // chrome.tabGroups.move
  i as getSessionStorage, // chrome.storage.session.get
  j as setSessionStorage, // chrome.storage.session.set
  n as makeReactive, // reactive proxy for arrays/objects
  o as html, // template literal HTML rendering
  p as mapTemplate // maps array to template fragments
} from "./chrome-utils.js";

/**
 * Fires a custom event on a given element.
 * @param {Element} element - The element to fire the event on.
 * @param {string} eventName - The event name.
 * @param {*} detail - The event detail.
 */
function fireEvent(element, eventName, detail) {
  element.dispatchEvent(new CustomEvent(eventName, { detail, bubbles: true }));
}

/**
 * Shorthand for querySelector.
 * @param {string} selector 
 * @param {Element|Document} [root=document]
 * @returns {Element|null}
 */
function $(selector, root = document) {
  return root.querySelector(selector);
}

/**
 * Shorthand for adding an event listener.
 * @param {Element} element 
 * @param {string} event 
 * @param {Function} handler 
 */
function $on(element, event, handler) {
  element.addEventListener(event, handler);
}

/**
 * Custom element: <window-list>
 * Displays a list of open windows, allows switching and renaming.
 */
class WindowList extends HTMLElement {
  constructor() {
    super();
    /** @type {number[]} */
    this.windows = [];
    /** @type {number} */
    this.currentWindowId = null;
  }

  /**
   * Handles switching to a different window.
   * @param {Event} event 
   */
  onWindowSwitch(event) {
    globalThis.windowId = +event.target.value;
    fireEvent(document.body, "windowswitch", globalThis.windowId);
  }

  /**
   * Handles click events for renaming a window.
   * @param {{target: Element}} param0 
   */
  onWindowClick({ target }) {
    if (!target.closest("vt-icon")) return;
    const listItem = target.closest("li");
    const dialog = new UpdateWindowDialog(listItem.textContent);
    $on(dialog, "update", ({ detail: newName }) => {
      listItem.firstElementChild.textContent = newName;
      const windowId = +listItem.id;
      setSessionStorage({ [windowId]: newName });
      if (this.currentWindowId === windowId) {
        $(".win-title").textContent = newName.slice(0, 10);
      }
    });
    this.firstElementChild.appendChild(dialog);
  }

  /**
   * Adds a window to the list.
   * @param {{id: number}} param0 
   */
  addWindow({ id }) {
    this.windows.push(id);
  }

  /**
   * Removes a window from the list.
   * @param {number} windowId 
   */
  removeWindow(windowId) {
    const index = this.windows.findIndex(w => w.id === windowId);
    this.windows.splice(index, 1);
  }

  /**
   * Renders the window list UI.
   * @param {Object.<number, string>} windowNames - Map of windowId to name.
   * @returns {DocumentFragment}
   */
  render(windowNames) {
    const renderWindowItem = (windowId) => html`<li id="${windowId}">
      <label>
        <input type="radio" name="window" value="${windowId}" hidden />
        <span>${windowNames[windowId]?.slice(0, 12) ?? windowId}</span>
      </label>
      <vt-icon ico="edit"></vt-icon>
    </li>`;

    return html`
      <window-list-popup
        id="window-list"
        style="left:2em"
        @change=${this.onWindowSwitch.bind(this)}
        @click=${this.onWindowClick.bind(this)}
        popover>
        ${mapTemplate(this.windows, renderWindowItem)}
      </window-list-popup>
      <div style="padding-top: 0.2em;">
        <span class="win-title">${windowNames[this.currentWindowId]?.slice(0, 11) ?? this.currentWindowId}</span>
        <button popovertarget="window-list">
          <vt-icon ico="chev-down" title="Switch window" toggle></vt-icon>
        </button>
      </div>
    `;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  async connectedCallback() {
    // Get all normal windows and their IDs
    const windowIds = (await chrome.windows.getAll({ windowTypes: ["normal"] })).map(w => w.id);
    this.windows = makeReactive(windowIds);
    this.currentWindowId = (await chrome.windows.getCurrent()).id;
    const windowNames = await getSessionStorage(null);
    windowNames[this.currentWindowId] ??= i18n("this_window");
    this.replaceChildren(this.render(windowNames));
    this.setListener();
    $(`input[value="${this.currentWindowId}"]`, this).checked = true;
    $on(
      this.firstElementChild,
      "toggle",
      (e) => e.newState === "closed" && ($('vt-icon[ico="chev-down"]', this).checked = false)
    );
  }

  /**
   * Sets up Chrome window event listeners.
   */
  setListener() {
    chrome.windows.onCreated.addListener(this.addWindow.bind(this), {
      windowTypes: ["normal"],
    });
    chrome.windows.onRemoved.addListener(this.removeWindow.bind(this), {
      windowTypes: ["normal"],
    });
  }
}
customElements.define("window-list", WindowList);

/**
 * Custom element: <update-window-dialog>
 * Dialog for renaming a window.
 */
class UpdateWindowDialog extends HTMLDialogElement {
  /**
   * @param {string} winName 
   */
  constructor(winName) {
    super();
    this.winName = winName;
    /** @type {HTMLElement|null} */
    this.emojiPicker = null;
  }

  /**
   * Fires update event and closes dialog.
   */
  updateWindow() {
    fireEvent(this, "update", $("input", this).value);
    this.remove();
  }

  /**
   * Shows emoji picker for window name.
   * @param {{target: Element}} param0 
   */
  async showEmojiPicker({ target }) {
    if (this.emojiPicker) return this.emojiPicker.showPopover();
    const { EmojiPicker } = await import("./emoji-picker-GN6LRT62.js");
    this.emojiPicker = new EmojiPicker();
    target.before(this.emojiPicker);
  }

  /**
   * Renders the dialog UI.
   * @returns {DocumentFragment}
   */
  render() {
    return html`
      <h2>${i18n("update_window_name")}</h2>
      <label>
        <span>${i18n("name")}</span> <br />
        <input type="text" />
        <span class="emoji-btn" title="Pick emoji" @click=${this.showEmojiPicker.bind(this)}> 😃 </span>
      </label>
      <div>
        <button class="outline-btn" @click=${this.remove.bind(this)}>${i18n("cancel")}</button>
        <button @click=${this.updateWindow.bind(this)}>${i18n("update")}</button>
      </div>
    `;
  }

  /**
   * Lifecycle: called when dialog is added to DOM.
   */
  connectedCallback() {
    this.id = "update-win-dialog";
    this.replaceChildren(this.render());
    this.showModal();
    $on(this, "toggle", (e) => e.newState === "closed" && this.remove());
  }
}
customElements.define("update-window-dialog", UpdateWindowDialog, { extends: "dialog" });

/**
 * Custom element: <window-select>
 * Allows moving tabs or tab groups to another window or a new window.
 */
class WindowSelect extends HTMLElement {
  /**
   * @param {number[]} tabIds 
   * @param {number} tabGroupId 
   */
  constructor(tabIds, tabGroupId) {
    super();
    /** @type {number[]} */
    this.tabIds = tabIds;
    /** @type {number} */
    this.tabGroupId = tabGroupId;
    /** @type {number[]} */
    this.windowIds = [];
  }

  /**
   * Moves tabs or tab group to selected window or creates a new window.
   * @param {{target: HTMLInputElement}} param0 
   */
  async moveToWindow({ target }) {
    try {
      const moveOptions = {
        index: -1,
        windowId: +target.value || (await chrome.windows.create({ state: "maximized" })).id,
      };
      if (this.tabIds) {
        await chrome.tabs.move(this.tabIds, moveOptions);
      } else if (this.tabGroupId) {
        await moveTabGroup(this.tabGroupId, moveOptions);
      }
      this.remove();
      $("marked-action")?.remove();
    } catch {
      console.error();
    }
  }

  /**
   * Handles click events for renaming a window or creating a new window.
   * @param {{target: Element}} param0 
   */
  onWindowClick({ target }) {
    const listItem = target.closest("li");
    if (listItem.id === "newWindow") return this.moveToWindow({ target });
    if (!target.closest("vt-icon")) return;
    const dialog = new UpdateWindowDialog(listItem.textContent);
    $on(dialog, "update", ({ detail: newName }) => {
      listItem.firstElementChild.textContent = newName;
    });
    this.firstElementChild.appendChild(dialog);
  }

  /**
   * Renders the window selection UI.
   * @returns {DocumentFragment}
   */
  render() {
    const renderWindowItem = (windowId) => html`<li>
      <label>
        <input type="radio" name="window" value="${windowId}" hidden />
        <span>${windowId}</span>
      </label>
      <vt-icon ico="edit" title="Rename window"></vt-icon>
    </li>`;

    return html`
      <li>
        <select name="position" disabled>
          <option value="-1">window's end</option>
          <option value="5">window's middle</option>
          <option value="0">window's start</option>
        </select>
      </li>
      ${this.windowIds.map(renderWindowItem)}
      <li id="newWindow">
        <cite>${i18n("new_window")}</cite>
        <tv-icon ico="plus"></tv-icon>
      </li>
    `;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  async connectedCallback() {
    this.id = "window-select";
    this.style.left = "4em";
    this.setAttribute("popover", "");
    this.windowIds = (await chrome.windows.getAll({ windowTypes: ["normal"] })).map(({ id }) => id);
    this.replaceChildren(this.render());
    this.showPopover();
    $on(this, "change", this.moveToWindow);
    $on(this, "click", this.onWindowClick);
    $on(this, "toggle", (e) => e.newState === "closed" && this.remove());
  }
}
customElements.define("window-select", WindowSelect);

// Export for use in other modules
export { WindowList as WindowListElement, WindowSelect as WindowSelectElement };
