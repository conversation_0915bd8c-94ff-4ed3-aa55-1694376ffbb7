import { k as GROUP_COLORS, o as html } from "./chrome-utils.js";
import tabGroupUpdatorStyles from "./tabgroup-updator-BSQZAEDQ.css" with { type: "css" };

/**
 * Custom element for creating or updating a Chrome tab group.
 * Provides UI for selecting color, editing title, and submitting changes.
 * 
 * @class TabGroupUpdatorElement
 * @extends HTMLElement
 */
class TabGroupUpdatorElement extends HTMLElement {
  /**
   * @type {HTMLInputElement}
   * Reference to the title input element.
   */
  titleInput;

  /**
   * @type {Object}
   * The tab group object, if updating an existing group.
   */
  tabGroup;

  /**
   * @type {number[]}
   * Array of tab IDs to group.
   */
  tabIds;

  /**
   * @type {string}
   * Selected color for the tab group.
   */
  color;

  /**
   * @constructor
   * @param {Object} tabGroup - The tab group object (optional, for update).
   * @param {number[]} tabIds - Array of tab IDs to group.
   */
  constructor(tabGroup, tabIds) {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [tabGroupUpdatorStyles];
    this.tabGroup = tabGroup;
    this.tabIds = tabIds;
    this.color = tabGroup?.color || GROUP_COLORS[0];
  }

  /**
   * Handles updating or creating the tab group using Chrome APIs.
   * Removes the element and any marked actions on success.
   * Logs errors to the console.
   * @returns {Promise<void>}
   */
  async updateGroup() {
    try {
      // Use existing group ID or create a new group
      let groupId =
        this.tabGroup?.id ??
        (await chrome.tabs.group({
          tabIds: this.tabIds,
          createProperties: { windowId: globalThis.windowId },
        }));

      // Update group properties
      await chrome.tabGroups.update(groupId, {
        collapsed: false,
        title: this.titleInput.value,
        color: this.color,
      });

      // Remove this UI and any marked actions
      this.remove();
      document.querySelector("marked-action")?.remove();
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * Handles color selection from the color picker.
   * @param {Event} event - The change event from the color input.
   */
  onColorPick({ target }) {
    this.color = target.value;
  }

  /**
   * Renders the UI for color selection, title input, and submit button.
   * Uses lit-html style template rendering.
   * @returns {DocumentFragment}
   */
  render() {
    /**
     * Renders a color radio button.
     * @param {string} color
     * @returns {TemplateResult}
     */
    const renderColorOption = (color) => html`<label style="--clr:${color}">
      <input type="radio" name="group-color" value="${color}" hidden />
    </label>`;

    return html`
      <div class="color-box" @change=${this.onColorPick.bind(this)}>
        ${GROUP_COLORS.map(renderColorOption)}
      </div>
      <input
        type="text"
        part="title-input"
        list="group_titles"
        value="${this.tabGroup?.title ?? ""}"
        ref=${(el) => (this.titleInput = el)}
        placeholder="${i18n("update_tabgroup_title")}" />
      <button @click=${this.updateGroup.bind(this)}>
        ${this.tabGroup ? "Update" : "Create"}
      </button>
      <datalist id="group_titles"></datalist>
    `;
  }

  /**
   * Lifecycle callback when the element is added to the DOM.
   * Sets up styles, renders UI, populates datalist, and popover behavior.
   */
  connectedCallback() {
    this.id = "tabgroup-updator";
    this.style.padding = "0.6em";
    this.setAttribute("popover", "");
    this.shadowRoot.replaceChildren(this.render());
    this.addDataList();
    this.showPopover();
    $on(this, "toggle", (event) => event.newState === "closed" && this.remove());
  }

  /**
   * Populates the datalist with existing tab group titles from storage.
   * Logs errors to the console.
   * @returns {Promise<void>}
   */
  async addDataList() {
    try {
      const { tabGroups } = await getStore("tabGroups");
      if (!tabGroups) return;
      const fragment = new DocumentFragment();
      for (const title of Object.keys(tabGroups)) {
        const option = document.createElement("option");
        option.value = title;
        fragment.appendChild(option);
      }
      this.shadowRoot.lastElementChild.appendChild(fragment);
    } catch (error) {
      console.error(error);
    }
  }
}

customElements.define("tabgroup-updator", TabGroupUpdatorElement);
export { TabGroupUpdatorElement as a };
