/**
 * @fileoverview
 * Window management UI components for a Chrome extension.
 * Provides custom elements for window selection, window renaming, and moving tabs/groups between windows.
 * All logic is fully deobfuscated, documented, and uses modern idioms.
 */

import {
  moveTabGroup,
  getSessionStorage,
  setSessionStorage,
  bindReactiveArray,
  makeReactive,
  renderTemplate
} from "./chrome-utils.js";

/**
 * Fires a custom event on a target element.
 * @param {Element} target
 * @param {string} eventName
 * @param {*} detail
 */
function fireEvent(target, eventName, detail) {
  target.dispatchEvent(new CustomEvent(eventName, { bubbles: true, detail }));
}

/**
 * Shorthand for querySelector within a context (defaults to document).
 * @param {string} selector
 * @param {ParentNode} [context=document]
 * @returns {Element|null}
 */
function $(selector, context = document) {
  return context.querySelector(selector);
}

/**
 * Shorthand for adding an event listener.
 * @param {Element} el
 * @param {string} event
 * @param {Function} handler
 */
function $on(el, event, handler) {
  el.addEventListener(event, handler);
}

/**
 * Custom element: <window-list>
 * Displays a list of open Chrome windows and allows switching/renaming.
 */
class WindowListElement extends HTMLElement {
  constructor() {
    super();
    /** @type {number[]} */
    this.windows = [];
    /** @type {number} */
    this.currentWindowId = null;
  }

  /**
   * Handles switching to a different window.
   * @param {Event} event
   */
  onWindowSwitch(event) {
    globalThis.windowId = +event.target.value;
    fireEvent(document.body, "windowswitch", globalThis.windowId);
  }

  /**
   * Handles clicking the edit icon to rename a window.
   * @param {Event} event
   */
  onWindowClick({ target }) {
    if (!target.closest("vt-icon")) return;
    const listItem = target.closest("li");
    const dialog = new UpdateWindowDialog(listItem.textContent);

    $on(dialog, "update", ({ detail: newName }) => {
      listItem.firstElementChild.textContent = newName;
      const windowId = +listItem.id;
      setSessionStorage({ [windowId]: newName });
      if (this.currentWindowId === windowId) {
        $(".win-title").textContent = newName.slice(0, 10);
      }
    });

    this.firstElementChild.appendChild(dialog);
  }

  /**
   * Adds a window to the list.
   * @param {{ id: number }} param0
   */
  addWindow({ id }) {
    this.windows.push(id);
  }

  /**
   * Removes a window from the list.
   * @param {number} windowId
   */
  removeWindow(windowId) {
    const idx = this.windows.findIndex(w => w.id === windowId);
    this.windows.splice(idx, 1);
  }

  /**
   * Renders the window list UI.
   * @param {Object<number, string>} windowNames
   * @returns {DocumentFragment}
   */
  render(windowNames) {
    const renderWindowItem = (windowId) => renderTemplate`<li id="${windowId}">
      <label>
        <input type="radio" name="window" value="${windowId}" hidden />
        <span>${windowNames[windowId]?.slice(0, 12) ?? windowId}</span>
      </label>
      <vt-icon ico="edit"></vt-icon>
    </li>`;

    return renderTemplate`<window-list-popup
      id="window-list"
      style="left:2em"
      @change=${this.onWindowSwitch.bind(this)}
      @click=${this.onWindowClick.bind(this)}
      popover>
      ${bindReactiveArray(this.windows, renderWindowItem)}
    </window-list-popup>
    <div style="padding-top: 0.2em;">
      <span class="win-title">${windowNames[this.currentWindowId]?.slice(0, 11) ?? this.currentWindowId}</span>
      <button popovertarget="window-list">
        <vt-icon ico="chev-down" title="Switch window" toggle></vt-icon>
      </button>
    </div>`;
  }

  /**
   * Initializes the element and populates the window list.
   */
  async connectedCallback() {
    // Get all normal Chrome windows
    const windowIds = (await chrome.windows.getAll({ windowTypes: ["normal"] })).map(w => w.id);
    this.windows = makeReactive(windowIds);
    this.currentWindowId = (await chrome.windows.getCurrent()).id;

    // Get window names from session storage
    const windowNames = await getSessionStorage(null);
    windowNames[this.currentWindowId] ??= i18n("this_window");

    this.replaceChildren(this.render(windowNames));
    this.setListeners();

    // Set the current window as checked
    $(`input[value="${this.currentWindowId}"]`, this).checked = true;

    // Listen for popup close to uncheck the icon
    $on(
      this.firstElementChild,
      "toggle",
      (e) => {
        if (e.newState === "closed") {
          $('vt-icon[ico="chev-down"]', this).checked = false;
        }
      }
    );
  }

  /**
   * Sets up listeners for Chrome window creation/removal.
   */
  setListeners() {
    chrome.windows.onCreated.addListener(this.addWindow.bind(this), {
      windowTypes: ["normal"],
    });
    chrome.windows.onRemoved.addListener(this.removeWindow.bind(this), {
      windowTypes: ["normal"],
    });
  }
}
customElements.define("window-list", WindowListElement);

/**
 * Custom element: <dialog is="update-window-dialog">
 * Dialog for renaming a window.
 */
class UpdateWindowDialog extends HTMLDialogElement {
  /**
   * @param {string} winName
   */
  constructor(winName) {
    super();
    this.winName = winName;
    /** @type {HTMLElement|null} */
    this.emojiPicker = null;
  }

  /**
   * Fires the update event and closes the dialog.
   */
  updateWindow() {
    fireEvent(this, "update", $("input", this).value);
    this.remove();
  }

  /**
   * Shows the emoji picker for the window name.
   * @param {Event} event
   */
  async showEmojiPicker({ target }) {
    if (this.emojiPicker) return this.emojiPicker.showPopover();
    const { EmojiPicker } = await import("./emoji-picker-6IWPZZFB.js");
    this.emojiPicker = new EmojiPicker();
    target.before(this.emojiPicker);
  }

  /**
   * Renders the dialog UI.
   * @returns {DocumentFragment}
   */
  render() {
    return renderTemplate`<h2>${i18n("update_window_name")}</h2>
      <label>
        <span>${i18n("name")}</span> <br />
        <input type="text" />
        <span class="emoji-btn" title="Pick emoji" @click=${this.showEmojiPicker.bind(this)}> 😃 </span>
      </label>
      <div>
        <button class="outline-btn" @click=${this.remove.bind(this)}>${i18n("cancel")}</button>
        <button @click=${this.updateWindow.bind(this)}>${i18n("update")}</button>
      </div>`;
  }

  /**
   * Initializes the dialog and shows it.
   */
  connectedCallback() {
    this.id = "update-win-dialog";
    this.replaceChildren(this.render());
    this.showModal();
    $on(this, "toggle", (e) => {
      if (e.newState === "closed") this.remove();
    });
  }
}
customElements.define("update-window-dialog", UpdateWindowDialog, { extends: "dialog" });

/**
 * Custom element: <window-select>
 * Allows moving tabs or tab groups to another window or a new window.
 */
class WindowSelectElement extends HTMLElement {
  /**
   * @param {number[]|null} tabIds
   * @param {number|null} tabGroupId
   */
  constructor(tabIds = null, tabGroupId = null) {
    super();
    /** @type {number[]|null} */
    this.tabIds = tabIds;
    /** @type {number|null} */
    this.tabGroupId = tabGroupId;
    /** @type {number[]} */
    this.windowIds = [];
  }

  /**
   * Moves tabs or tab group to the selected window or creates a new window.
   * @param {Event} event
   */
  async moveToWindow({ target }) {
    try {
      const moveOptions = {
        index: -1,
        windowId: +target.value || (await chrome.windows.create({ state: "maximized" })).id,
      };
      if (this.tabIds) {
        await chrome.tabs.move(this.tabIds, moveOptions);
      } else if (this.tabGroupId) {
        await moveTabGroup(this.tabGroupId, moveOptions);
      }
      this.remove();
      $("marked-action")?.remove();
    } catch {
      console.error();
    }
  }

  /**
   * Handles clicking the edit icon to rename a window, or creating a new window.
   * @param {Event} event
   */
  onWindowClick({ target }) {
    const listItem = target.closest("li");
    if (listItem.id === "newWindow") return this.moveToWindow({ target });
    if (!target.closest("vt-icon")) return;
    const dialog = new UpdateWindowDialog(listItem.textContent);

    $on(dialog, "update", ({ detail: newName }) => {
      listItem.firstElementChild.textContent = newName;
    });

    this.firstElementChild.appendChild(dialog);
  }

  /**
   * Renders the window selection UI.
   * @returns {DocumentFragment}
   */
  render() {
    const renderWindowItem = (windowId) => renderTemplate`<li>
      <label>
        <input type="radio" name="window" value="${windowId}" hidden />
        <span>${windowId}</span>
      </label>
      <vt-icon ico="edit" title="Rename window"></vt-icon>
    </li>`;

    return renderTemplate`
      <li>
        <select name="position" disabled>
          <option value="-1">window's end</option>
          <option value="5">window's middle</option>
          <option value="0">window's start</option>
        </select>
      </li>
      ${this.windowIds.map(renderWindowItem)}
      <li id="newWindow">
        <cite>${i18n("new_window")}</cite>
        <tv-icon ico="plus"></tv-icon>
      </li>
    `;
  }

  /**
   * Initializes the element and populates the window list.
   */
  async connectedCallback() {
    this.id = "window-select";
    this.style.left = "4em";
    this.setAttribute("popover", "");
    this.windowIds = (await chrome.windows.getAll({ windowTypes: ["normal"] })).map(w => w.id);
    this.replaceChildren(this.render());
    this.showPopover();
    $on(this, "change", this.moveToWindow.bind(this));
    $on(this, "click", this.onWindowClick.bind(this));
    $on(this, "toggle", (e) => {
      if (e.newState === "closed") this.remove();
    });
  }
}
customElements.define("window-select", WindowSelectElement);

// Exports for use in other modules
export { WindowListElement as WindowList, WindowSelectElement as WindowSelect };
