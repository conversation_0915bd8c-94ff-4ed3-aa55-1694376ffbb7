// Import utility functions and template rendering
import { k as colorList, o as html } from "./chrome-utils.js";
// Import CSS stylesheet for the component
import tabGroupUpdatorStyles from "./tabgroup-updator-BSQZAEDQ.css" with { type: "css" };

/**
 * Custom element for creating or updating a Chrome tab group.
 * Provides UI for selecting color and title, and updates the group via Chrome API.
 * 
 * @class TabGroupUpdatorElement
 * @extends HTMLElement
 */
class TabGroupUpdatorElement extends HTMLElement {
  /**
   * @type {HTMLInputElement}
   * Reference to the title input element.
   */
  titleInput;

  /**
   * @type {Object}
   * The tab group object, if updating an existing group.
   */
  tabGroup;

  /**
   * @type {number[]}
   * Array of tab IDs to group.
   */
  tabIds;

  /**
   * @type {string}
   * Selected color for the tab group.
   */
  color;

  /**
   * Constructs the element and attaches shadow DOM and styles.
   * @param {Object} tabGroup - The tab group object (optional).
   * @param {number[]} tabIds - The IDs of tabs to group.
   */
  constructor(tabGroup, tabIds) {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [tabGroupUpdatorStyles];
    this.tabGroup = tabGroup;
    this.tabIds = tabIds;
  }

  /**
   * Updates or creates the tab group using the Chrome API.
   * Handles UI removal and error logging.
   * @returns {Promise<void>}
   */
  async updateGroup() {
    try {
      // Use existing group ID or create a new group
      let groupId =
        this.tabGroup?.id ??
        (await chrome.tabs.group({
          tabIds: this.tabIds,
          createProperties: { windowId: globalThis.windowId },
        }));
      // Update group properties
      await chrome.tabGroups.update(groupId, {
        collapsed: false,
        title: this.titleInput.value,
        color: this.color,
      });
      // Remove the UI and any marked action
      this.remove();
      $("marked-action")?.remove();
    } catch (err) {
      console.error(err);
    }
  }

  /**
   * Handles color selection from the color radio buttons.
   * @param {Event} event - The change event from the color input.
   */
  onColorPick({ target }) {
    this.color = target.value;
  }

  /**
   * Renders the component UI using a template literal.
   * @returns {HTMLElement}
   */
  render() {
    // Template for each color radio button
    const colorRadioTemplate = (color) => html`<label style="--clr:${color}">
      <input type="radio" name="group-color" value="${color}" hidden />
    </label>`;

    return html`
      <div class="color-box" @change=${this.onColorPick.bind(this)}>
        ${colorList.map(colorRadioTemplate)}
      </div>
      <input
        type="text"
        part="title-input"
        list="group_titles"
        value="${this.tabGroup?.title ?? ""}"
        ref=${(el) => (this.titleInput = el)}
        placeholder="${i18n("update_tabgroup_title")}" />
      <button @click=${this.updateGroup.bind(this)}>
        ${this.tabGroup ? "Update" : "Create"}
      </button>
      <datalist id="group_titles"></datalist>
    `;
  }

  /**
   * Lifecycle method called when the element is added to the DOM.
   * Sets up styles, attributes, renders UI, and event listeners.
   */
  connectedCallback() {
    this.id = "tabgroup-updator";
    this.style.padding = "0.6em";
    this.setAttribute("popover", "");
    this.shadowRoot.replaceChildren(this.render());
    this.addDataList();
    this.showPopover();
    // Remove the element if the popover is closed
    $on(this, "toggle", (event) => event.newState === "closed" && this.remove());
  }

  /**
   * Populates the datalist with existing tab group titles from storage.
   * @returns {Promise<void>}
   */
  async addDataList() {
    try {
      let { tabGroups } = await getStore("tabGroups");
      if (!tabGroups) return;
      let fragment = new DocumentFragment();
      for (let title of Object.keys(tabGroups)) {
        let option = document.createElement("option");
        option.value = title;
        fragment.appendChild(option);
      }
      this.shadowRoot.lastElementChild.appendChild(fragment);
    } catch (err) {
      console.error(err);
    }
  }
}

// Register the custom element
customElements.define("tabgroup-updator", TabGroupUpdatorElement);
// Export the class for use elsewhere
export { TabGroupUpdatorElement as TabGroupUpdator };
