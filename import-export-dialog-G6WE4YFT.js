import { createTabInWindow, getWindowTitles, html as renderHtml } from "./chrome-utils.js";
import "./chunk-EK7ODJWE.js";

/**
 * Utility class for exporting tab data in various formats.
 */
class TabExportUtility {
  /**
   * Stores window titles for HTML export.
   * @type {Record<number, string>}
   */
  windowTitles = {};

  constructor() {}

  /**
   * Export tabs as JSON string.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string}
   */
  toJson(windowData) {
    const tabList = windowData.tabs.map(({ title, url, favIconUrl }) => ({
      title,
      url,
      favIconUrl,
    }));
    return JSON.stringify(tabList);
  }

  /**
   * Export tabs as CSV string.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string}
   */
  toCsv(windowData) {
    const header = `url,title,favIconUrl
`;
    const rows = windowData.tabs
      .map(({ title, url, favIconUrl }) => `${url},${title},${favIconUrl}`)
      .join(`
`);
    return header + rows;
  }

  /**
   * Export tabs as Markdown list.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string}
   */
  toMarkdown(windowData) {
    return windowData.tabs
      .map(({ title, url }) => `[${title}](${url})`)
      .join(`
`);
  }

  /**
   * Export tabs as HTML document.
   * @param {Array<{ id: number, tabs: Array<{title: string, url: string, favIconUrl: string}> }>} windows
   * @returns {string}
   */
  toHtml(windows) {
    const docStart = `<!DOCTYPE html>
		<html lang="en">
		<head>
			<meta charset="UTF-8">
			<meta name="viewport" content="width=device-width, initial-scale=1.0">
			<title>Tabs at ${new Date().toLocaleString("default", { dateStyle: "full" })}</title>
		</head>
		<body>
		`;
    // Renders a single tab as a list item
    const renderTab = (tab) => `<li style="padding-block: 0.2em">
            <a href="${tab.url}" target="_blank" rel="noopener noreferrer">${tab.title}</a>
        </li>`;
    // Renders a window section with its tabs
    const renderWindow = (window) => `<details>
            <summary>${this.windowTitles[window.id] ?? window.id}</summary>
            <ol>
                ${window.tabs.map(renderTab).join("")}
            </ol>
        </details>`;
    return `${docStart}<blockquote >
            <h1>${new Date().toLocaleString("default", { dateStyle: "full" })}</h1>
            ${windows.map(renderWindow).join(`
`)}
        </blockquote></body></html>`;
  }

  /**
   * Export tabs in the selected format and trigger download.
   * @param {"json"|"csv"|"markdown"|"html"} format
   * @returns {Promise<void>}
   */
  async export(format) {
    // Get all normal windows and their tabs
    const windows = await chrome.windows.get(windowId, {
      windowTypes: ["normal"],
      populate: true,
    });
    // Get window titles for HTML export
    this.windowTitles = await getWindowTitles(null);
    // Generate export content
    const content = this[`to${format.charAt(0).toUpperCase() + format.slice(1)}`](windows);
    this.downloadFile(content, format);
  }

  /**
   * Download a string as a file.
   * @param {string} content
   * @param {string} format
   */
  downloadFile(content, format) {
    const blob = new Blob([content], { type: "text/plain" });
    const anchor = document.createElement("a");
    anchor.setAttribute("href", URL.createObjectURL(blob));
    anchor.setAttribute("download", new Date().toISOString() + "." + format);
    anchor.click();
  }
}

/**
 * Custom element for the import/export dialog.
 * Handles tab import/export UI and logic.
 */
class ImportExportDialog extends HTMLElement {
  constructor() {
    super();
  }

  /**
   * Handles file upload for importing tabs.
   * Extracts URLs from the uploaded file and opens them in new tabs.
   * @param {Event} event
   */
  onFileUpload(event) {
    const urlRegex = /https:\/\/[^\s,"')\n]+/g;
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.onload = async (e) => {
      const text = e.target.result;
      if (typeof text !== "string") return;
      const matches = text.matchAll(urlRegex);
      for (const match of matches) {
        const url = match[0];
        if (url && URL.canParse(url)) {
          // Open the URL in a new tab in the current window
          const { id: tabId } = await createTabInWindow({ url, windowId });
          // Wait a bit before discarding (unloading) the tab
          await new Promise((resolve) => setTimeout(resolve, 1000));
          await chrome.tabs.discard(tabId);
        }
      }
    };
    reader.onerror = (e) => console.error(e);
    reader.readAsText(file);
  }

  /**
   * Exports tabs in the selected format.
   */
  exportTabs() {
    new TabExportUtility().export($("select", this).value);
  }

  /**
   * Scrolls to the selected tab section in the dialog.
   * @param {{ target: { value: string } }} param0
   */
  switchTab({ target }) {
    $("#" + target.value, this).scrollIntoView({
      behavior: "smooth",
      inline: "start",
    });
  }

  /**
   * Renders the dialog UI.
   * @returns {any}
   */
  render() {
    return renderHtml`<header @change=${this.switchTab.bind(this)}>
				<label>${i18n("import")} <input type="radio" name="import-export" value="import-tab" hidden /> </label>
				<label
					>${i18n("export")} <input type="radio" name="import-export" value="export-tab" hidden checked />
				</label>
			</header>
			<div>
				<section id="import-tab">
					<input type="file" accept="text/html,text/csv,application/json" @change=${this.onFileUpload} />
					<button>${i18n("import")}</button>
				</section>
				<section id="export-tab">
					<label>
						<span>${i18n("format")}</span>
						<select name="format">
							<option value="html">html</option>
							<option value="json">json</option>
							<option value="markdown">markdown</option>
							<option value="csv">csv</option>
							<option value="sql">SQL</option>
						</select>
					</label>
					<button @click=${this.exportTabs.bind(this)}>Export</button>
				</section>
			</div> `;
  }

  /**
   * Lifecycle callback when the element is added to the DOM.
   */
  connectedCallback() {
    this.style.top = "import-export";
    this.setAttribute("popover", "");
    this.replaceChildren(this.render());
    this.showPopover();
    $("#export-tab", this).scrollIntoView();
  }
}

customElements.define("import-export-dialog", ImportExportDialog);
export { ImportExportDialog };
