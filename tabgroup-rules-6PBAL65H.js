import {
  Rule,
  loadRules,
  saveRule,
  updateRule,
  deleteRule,
  ErrorDialog,
} from "./chunk-EEQB4VOH.js";

import {
  getTabGroups,
  colorOptions,
  transformRule,
  html,
  map,
  getStore,
  i18n,
  styleSheet,
} from "./chrome-utils.js";

import "./chunk-EK7ODJWE.js";
/**
 * Class representing a tab group rule processor
 */
class TabGroupRuleProcessor {
  constructor() {}

  /**
   * Fetches URLs from the tab group rules
   * @returns {Promise<void>}
   */
  async getUrls() {
    const tabData = await getTabGroups({});
    this.tabUrls = tabData.map((tab) => new URL(tab.url));
    this.titles = tabData.map((tab) => tab.title);
  }

  /**
   * Gets the top-level domains from tab URLs
   * @returns {string[]} Array of top-level domains
   */
  topDomains() {
    return this.tabUrls.map((url) =>
      url.host.slice(url.host.lastIndexOf(".", url.host.lastIndexOf(".") - 1) + 1)
    );
  }

  /**
   * Gets the full domains from tab URLs
   * @returns {string[]} Array of domains
   */
  domains() {
    return this.tabUrls.map((url) => url.host);
  }

  /**
   * Gets the first path segment from tab URLs
   * @returns {string[]} Array of first path segments
   */
  firstPathSegments() {
    return this.tabUrls.map((url) =>
      url.pathname.slice(1, url.pathname.indexOf("/", 1))
    );
  }

  /**
   * Gets the first two path segments from tab URLs
   * @returns {string[]} Array of first two path segments
   */
  twoPathSegments() {
    return this.tabUrls.map((url) =>
      url.pathname.slice(
        1,
        url.pathname.indexOf("/", url.pathname.indexOf("/", 1) + 1)
      )
    );
  }

  /**
   * Gets words from tab titles that are longer than 3 characters
   * @returns {string[]} Array of title words
   */
  titleWords() {
    return this.titles.flatMap((title) =>
      title.split(" ").filter((word) => word.length > 3)
    );
  }
};
/**
 * Class representing a tab group rule element
 */
class TabGroupRuleElement extends HTMLElement {
  /**
   * Create a TabGroupRuleElement
   * @param {Object} groupRule - The group rule data
   */
  constructor(groupRule) {
    super();
    this.groupRule = groupRule;
  }

  /**
   * Edit the group rule
   */
  editGroupRule() {
    const editDialog = new RuleEditDialog(this.groupRule);
    this.parentElement.parentElement.appendChild(editDialog);
  }

  /**
   * Delete the group rule
   * @returns {Promise<void>}
   */
  async deleteGroupRule() {
    try {
      await deleteRule(this.groupRule.id);
      this.remove();
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * Update rule properties
   */
  updateRuleProp = {
    /**
     * Update the rule name
     * @param {Object} event - The change event
     */
    name: ({ target: event }) => {
      updateRule(this.groupRule.id, "name", event.value);
      this.groupRule.name = event.value;
    },
    /**
     * Update the custom group name
     * @param {Object} event - The change event
     * @returns {Promise<void>}
     */
    ctmGroupName: async ({ target: event }) => {
      updateRule(this.groupRule.id, "ctmGroupName", event.value);
      const tabGroups = await getTabGroups({ title: this.groupRule.ctmGroupTitle });
      this.groupRule.ctmGroupTitle = event.value;
      if (tabGroups.length !== 0) {
        chrome.tabGroups.update(tabGroups[0].id, { title: event.value });
      }
    },
    /**
     * Update the rule color
     * @param {Object} event - The change event
     * @returns {Promise<void>}
     */
    color: async ({ target: event }) => {
      updateRule(this.groupRule.id, "color", event.value);
      const tabGroups = await getTabGroups({ title: this.groupRule.ctmGroupTitle });
      this.groupRule.color = event.value;
      if (tabGroups.length !== 0) {
        chrome.tabGroups.update(tabGroups[0].id, { color: event.value });
      }
    },
    /**
     * Update the rule priority
     * @param {Object} event - The change event
     */
    priority: ({ target: event }) => {
      updateRule(this.groupRule.id, "priority", event.value);
      this.groupRule.priority = event.value;
    }
  };

  /**
   * Toggle the group rule enabled state
   * @param {Object} event - The change event
   */
  toggleGroupRule({ target: event }) {
    updateRule(this.groupRule.id, "enabled", event.checked);
  }

  /**
   * Render the rule element
   * @returns {TemplateResult} The rendered template
   */
  render() {
    const matchTypes = (() => {
      const types = [...Object.keys(this.groupRule.urlMatches)];
      if (this.groupRule.titleIncludes.length === 0) {
        types.push("title");
      }
      return types.join(",");
    })();

    return html`
      <input
        type="checkbox"
        name=""
        class="toggle_rule"
        @change=${this.toggleGroupRule.bind(this)}
      />
      <rule-details>
        <div class="left-column">
          <label>
            <span>${i18n("name")}:</span>
            <input
              type="text"
              value=${this.groupRule.name}
              @change=${this.updateRuleProp.name}
            />
          </label>
          <label>
            <span>${i18n("group")}:</span>
            <input
              type="text"
              value="${this.groupRule.ctmGroupTitle ?? "auto"}"
              @change=${this.updateRuleProp.ctmGroupName}
            />
          </label>
          <label>
            <span>${i18n("priority")}:</span>
            <input
              type="number"
              value=${this.groupRule.priority}
              @change=${this.updateRuleProp.priority}
            />
          </label>
        </div>
        <div class="center-column"></div>
        <div class="right-column">
          <div>
            <span>${i18n("matches")}:</span>
            <var title="${matchTypes}">${matchTypes.slice(0, 10)}</var>
          </div>
          <label>
            <span>${i18n("color")}:</span>
            <select
              id="color_select"
              .value=${this.groupRule.color}
              @change=${this.updateRuleProp.color}
            >
              <option value="blue">blue</option>
              <option value="red">red</option>
              <option value="yellow">yellow</option>
              <option value="green">green</option>
              <option value="cyan">cyan</option>
              <option value="purple">purple</option>
              <option value="pink">pink</option>
              <option value="orange">orange</option>
              <option value="grey">grey</option>
            </select>
          </label>
          <div style="justify-content: end">
            <vt-icon
              ico="edit"
              title="${i18n("edit_rule")}"
              class="edit-icon"
              @click=${this.editGroupRule.bind(this)}
            ></vt-icon>
            <vt-icon
              ico="delete"
              title="${i18n("delete_rule")}"
              class="delete-icon"
              @click=${this.deleteGroupRule.bind(this)}
            ></vt-icon>
          </div>
        </div>
      </rule-details>
    `;
  }

  /**
   * Called when the element is added to the DOM
   */
  connectedCallback() {
    this.id = this.groupRule.id;
    this.replaceChildren(this.render());
    if (this.groupRule.enabled) {
      this.firstElementChild.checked = true;
    }
  }
};
customElements.define("tabgroup-rule", TabGroupRuleElement);
/**
 * Update or add a tab group rule element
 * @param {Object} ruleData - The rule data
 * @returns {Promise<void>}
 */
async function updateOrAddRuleElement(ruleData) {
  const ruleElement = new TabGroupRuleElement(ruleData);
  const existingElement = document.querySelector(`#r${ruleData.id}`, ruleContainer);
  if (existingElement) {
    existingElement.replaceWith(ruleElement);
  } else {
    ruleContainer.appendChild(ruleElement);
  }

  const autoGrouping = await getStore("autoGroupingOn");
  if (!autoGrouping.autoGroupingOn) {
    await new Promise((resolve) => setTimeout(resolve, 2000));
    chrome.runtime.sendMessage({
      msg: "toggle_auto_grouping",
      autoGroupingOn: true,
    });
  }
}
let ruleContainer;

class TabGroupRuleList extends HTMLElement {
  constructor() {
    super();
    this.ruleContainer = this;
  }

  /**
   * Render rule elements
   * @param {Array} rules - The rules to render
   * @returns {Array} Array of rule elements
   */
  render(rules) {
    return rules.map((rule) => new TabGroupRuleElement(rule));
  }

  /**
   * Called when the element is added to the DOM
   * @returns {Promise<void>}
   */
  async connectedCallback() {
    const rules = await loadRules();
    this.replaceChildren(...this.render(rules));
  }
}
customElements.define("tabgroup-rule-list", TabGroupRuleList);
/**
 * Class representing a rule edit dialog
 */
class RuleEditDialog extends HTMLDialogElement {
  /**
   * Create a RuleEditDialog
   * @param {Object} ruleData - The rule data
   */
  constructor(ruleData) {
    super();
    this.rule = ruleData ?? new Rule();
  }

  /**
   * Set input values based on the rule data
   */
  setInputValue() {
    for (const matchType in this.rule.urlMatches) {
      document.querySelector(`input[value="${matchType}"]`, this).checked = true;
      document.querySelector(`input[name="${matchType}"]`, this).value =
        this.rule.urlMatches[matchType].join(",");
    }
    document.querySelector(`input[value="${this.rule.color}"]`, this).checked = true;
  }

  /**
   * Create a new rule
   * @returns {Promise<void>}
   */
  async createRule() {
    const newRule = { ...this.rule };
    newRule.urlMatches = {};
    for (const matchType in this.rule.urlMatches) {
      newRule.urlMatches[matchType] = [...this.rule.urlMatches[matchType]];
    }
    newRule.titleIncludes = [...this.rule.titleIncludes];

    try {
      await saveRule(newRule);
      updateOrAddRuleElement(structuredClone(newRule));
      this.remove();
    } catch (error) {
      console.error(error);
      document.body.appendChild(new ErrorDialog(error));
    }
  }

  /**
   * Remove a title match
   * @param {Object} event - The click event
   */
  removeMatchTitle({ currentTarget, target }) {
    const title = target.closest("li").textContent.trim();
    const index = this.rule.titleIncludes.indexOf(title);
    if (index !== -1) {
      this.rule.titleIncludes.splice(index, 1);
    }
    if (!target.closest("vt-icon")) {
      currentTarget.previousElementSibling.previousElementSibling.value = title;
    }
  }

  /**
   * Add a title match
   * @param {Object} event - The keyup event
   */
  addMatchTitle({ code, target }) {
    if (code === "Enter") {
      this.rule.titleIncludes.push(target.value);
      target.value = "";
    }
  }

  /**
   * Handle URL match changes
   * @param {Object} event - The change event
   */
  onUrlMatchChange(event) {
    const target = event.target;
    if (target.type === "checkbox") {
      const updateInput = (input) => {
        input.disabled = target.checked;
        if (target.checked) {
          delete this.rule.urlMatches[input.value];
        } else if (input.checked) {
          this.rule.urlMatches[input.value] =
            input.parentElement.nextElementSibling.value.trim().split(",");
        }
      };

      if (target.value === "two_path_segment" || target.value === "domain") {
        const input =
          event.target.closest("li").previousElementSibling.firstElementChild
            .firstElementChild;
        updateInput(input);
      }

      if (target.value === "ctm_match_pattern") {
        for (const input of event.currentTarget.querySelectorAll(
          'input[name="url-match"]'
        )) {
          if (input !== target) {
            updateInput(input);
          }
        }
      }

      if (target.checked) {
        const value = target.parentElement.nextElementSibling.value.trim();
        this.rule.urlMatches[target.value] = value ? value.split(",") : [];
      } else {
        delete this.rule.urlMatches[target.value];
      }
    } else if (target.previousElementSibling.firstElementChild.checked) {
      this.rule.urlMatches[target.name] = target.value.trim().split(",");
    }
  }

  /**
   * Handle color changes
   * @param {Object} event - The change event
   */
  onColorChange({ target }) {
    this.rule.color = target.value;
  }

  /**
   * Render the dialog
   * @param {TabGroupRuleProcessor} ruleProcessor - The rule processor
   * @returns {TemplateResult} The rendered template
   */
  render(ruleProcessor) {
    const renderChip = (title) =>
      html`<li class="chip-item">
        <span>${title}</span>
        <vt-icon ico="close" title="remove"></vt-icon>
      </li>`;

    const renderColorOption = (color) =>
      html`<label style="--clr:${color}">
        <input type="radio" name="group-color" value="${color}" hidden />
      </label>`;

    const renderOption = (value) => html`<option value="${value}"></option>`;

    return html`
      <vt-icon
        ico="close-circle"
        class="close-btn"
        @click=${this.remove.bind(this)}
      ></vt-icon>
      <label>
        <span>${i18n("rule_name")}</span>
        <input type="text" .value=${() => this.rule.name} />
      </label>

      <section class="title-match" style="margin-top:0.8em">
        <label>
          <span><vt-icon ico="title"></vt-icon> ${i18n("tab_title_includes")}</span>
          <input
            type="text"
            list="tabTitles"
            @keyup=${this.addMatchTitle.bind(this)}
          />
        </label>
        <datalist id="tabTitles">
          ${ruleProcessor.titleWords().map(renderOption)}
        </datalist>
        <ul class="chip-list" @click=${this.removeMatchTitle.bind(this)}>
          ${this.rule.titleIncludes.map(renderChip)}
        </ul>
      </section>
      <section class="url-match">
        <header>
          <vt-icon ico="route"></vt-icon>
          <span>${i18n("url_match")}</span>
        </header>
        <ul @change=${this.onUrlMatchChange.bind(this)}>
          <li>
            <label>
              <input type="checkbox" name="url-match" value="top_domain" />
              ${i18n("top_domain")}
            </label>
            <input
              type="email"
              name="top_domain"
              list="top_domains"
              placeholder="${i18n("enter_selected_top_domains")}"
              multiple
            />
            <datalist id="top_domains">
              ${ruleProcessor.topDomains().map(renderOption)}
            </datalist>
          </li>
          <li>
            <label>
              <input type="checkbox" name="url-match" value="domain" />
              ${i18n("domain")}
            </label>
            <input
              type="email"
              name="domain"
              list="domains"
              placeholder="${i18n("enter_selected_domains")}"
              multiple
            />
            <datalist id="domains">
              ${ruleProcessor.domains().map(renderOption)}
            </datalist>
          </li>
          <li>
            <label>
              <input
                type="checkbox"
                name="url-match"
                value="first_path_segment"
              />
              ${i18n("first_path_segment")}
            </label>
            <input
              type="email"
              name="first_path_segment"
              list="first_path_segments"
              placeholder="${i18n("selected_first_path")}"
              multiple
            />
            <datalist id="first_path_segments">
              ${ruleProcessor.firstPathSegments().map(renderOption)}
            </datalist>
          </li>
          <li>
            <label>
              <input
                type="checkbox"
                name="url-match"
                value="two_path_segment"
              />
              ${i18n("two_path_segment")}
            </label>
            <input
              type="email"
              name="two_path_segment"
              list="two_path_segments"
              placeholder="${i18n("selected_first_two_path")}"
              multiple
            />
            <datalist id="two-path-segments">
              ${ruleProcessor.twoPathSegments().map(renderOption)}
            </datalist>
          </li>
          <li>
            <label>
              <input type="checkbox" name="url-match" value="ctm_match_pattern" />
              <span>
                ${i18n("custom_url")}
                <a href="https://developer.chrome.com/docs/extensions/develop/concepts/match-patterns">
                  ${i18n("match_patterns")}
                </a>
              </span>
            </label>
            <input
              type="email"
              name="ctm_match_pattern"
              placeholder="https://*/foo*,http://google.es/*"
            />
          </li>
        </ul>
      </section>

      <section class="rule-row">
        <label>
          <span>${i18n("custom_tabgroup_title")}</span>
          <input
            type="text"
            .value=${() => this.rule.ctmGroupTitle}
            placeholder="(optional) custom title"
            style="width:80%"
          />
        </label>
        <label>
          <span>${i18n("priority")}</span>
          <input type="number" .value=${() => this.rule.priority} />
        </label>
      </section>

      <section>
        <span style="margin-bottom:0.4em">
          <vt-icon ico="color"></vt-icon> ${i18n("color")}
        </span>
        <div class="color-pot" @change=${this.onColorChange.bind(this)}>
          ${colorOptions.map(renderColorOption)}
        </div>
      </section>
      <button @click=${this.createRule.bind(this)}>
        ${i18n("create_rule")}
      </button>
    `;
  }

  /**
   * Called when the element is added to the DOM
   * @returns {Promise<void>}
   */
  async connectedCallback() {
    this.id = "create-rule-dialog";
    const ruleProcessor = new TabGroupRuleProcessor();
    await ruleProcessor.getUrls();
    this.replaceChildren(this.render(ruleProcessor));
    this.showModal();
    this.setInputValue();
  }
}
customElements.define("create-rule-dialog", RuleEditDialog, { extends: "dialog" });
import C from "./tabgroup-rules-ODJ7L3M6.css" with { type: "css" };
/**
 * Class representing a tab group rule dialog
 */
class TabGroupRuleDialog extends HTMLElement {
  /**
   * Create a TabGroupRuleDialog
   */
  constructor() {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [styleSheet];
  }

  /**
   * Show the rule creator dialog
   */
  showRuleCreator() {
    const dialog = new RuleEditDialog();
    this.shadowRoot.firstElementChild.appendChild(dialog);
  }

  /**
   * Render the dialog
   * @returns {Array} Array of elements to render
   */
  render() {
    return [
      html`<header>
        <vt-icon
          ico="close-circle"
          class="close-btn"
          @click=${this.remove.bind(this)}
        ></vt-icon>
        <span><vt-icon ico="group-rule"></vt-icon> ${i18n("tabgroup_rules")}</span>
        <button @click=${this.showRuleCreator.bind(this)}>
          <vt-icon ico="plus"></vt-icon> ${i18n("add_rule")}
        </button>
      </header>`,
      new TabGroupRuleList(),
    ];
  }

  /**
   * Called when the element is added to the DOM
   */
  connectedCallback() {
    const dialog = document.createElement("dialog");
    dialog.append(...this.render());
    this.shadowRoot.appendChild(dialog);
    dialog.showModal();
  }
}
customElements.define("tabgroup-rule-dialog", TabGroupRuleDialog);
export { TabGroupRuleDialog };
