// Imports (deobfuscated aliases for clarity)
import {
  a as getDefaultRule,
  b as fetchAllRules,
  d as saveRule,
  e as updateRuleProperty,
  f as deleteRule,
  g as ErrorDisplay,
} from "./chunk-MSKQ7G53.js";
import {
  a as fetchTabData,
  e as getGroupsByTitle,
  k as colorOptions,
  n as RuleEditorDialog,
  o as html,
  p as renderList,
} from "./chrome-utils.js";
import "./chunk-EK7ODJWE.js";

/**
 * Processes tab URLs and extracts relevant information.
 */
class TabUrlProcessor {
  constructor() {
    this.tabUrls = [];
    this.titles = [];
  }

  /**
   * Fetches and processes tab URLs and titles.
   */
  async getUrls() {
    const tabData = await fetchTabData({});
    this.tabUrls = tabData.map((tab) => new URL(tab.url));
    this.titles = tabData.map((tab) => tab.title);
  }

  /**
   * Extracts the top-level domain from each tab's URL.
   * @returns {string[]} Array of top-level domains.
   */
  topDomains() {
    return this.tabUrls.map((url) => {
      const parts = url.host.split('.');
      return parts.slice(-2).join('.');
    });
  }

  /**
   * Retrieves the full domain from each tab's URL.
   * @returns {string[]} Array of domains.
   */
  domains() {
    return this.tabUrls.map((url) => url.host);
  }

  /**
   * Extracts the first path segment from each tab's URL.
   * @returns {string[]} Array of first path segments.
   */
  firstPathSegments() {
    return this.tabUrls.map((url) => {
      const path = url.pathname.slice(1);
      return path.split('/')[0];
    });
  }

  /**
   * Extracts the first two path segments from each tab's URL.
   * @returns {string[]} Array of first two path segments.
   */
  twoPathSegments() {
    return this.tabUrls.map((url) => {
      const path = url.pathname.slice(1);
      const segments = path.split('/');
      return segments.slice(0, 2).join('/');
    });
  }

  /**
   * Extracts words from tab titles that are longer than 3 characters.
   * @returns {string[]} Array of title words.
   */
  titleWords() {
    return this.titles.flatMap((title) =>
      title.split(' ').filter((word) => word.length > 3)
    );
  }
}

/**
 * Custom HTML element for displaying and managing group rules.
 */
class GroupRuleElement extends HTMLElement {
  constructor(groupRule) {
    super();
    this.groupRule = groupRule;
  }

  /**
   * Opens the rule editor for modifying the group rule.
   */
  editGroupRule() {
    const ruleEditor = new RuleEditorDialog(this.groupRule);
    this.parentElement.parentElement.appendChild(ruleEditor);
  }

  /**
   * Deletes the group rule and removes the element.
   */
  async deleteGroupRule() {
    try {
      await deleteRule(this.groupRule.id);
      this.remove();
    } catch (err) {
      console.error(err);
    }
  }

  /**
   * Object containing methods to update rule properties.
   */
  updateRuleProp = {
    /**
     * Updates the rule name.
     * @param {Event} event - Input event.
     */
    name: ({ target: input }) => {
      updateRuleProperty(this.groupRule.id, "name", input.value);
      this.groupRule.name = input.value;
    },
    /**
     * Updates the custom group name and syncs with tab groups.
     * @param {Event} event - Input event.
     */
    ctmGroupName: async ({ target: input }) => {
      updateRuleProperty(this.groupRule.id, "ctmGroupName", input.value);
      const existingGroups = await getGroupsByTitle(this.groupRule.ctmGroupTitle);
      this.groupRule.ctmGroupTitle = input.value;
      if (existingGroups.length > 0) {
        chrome.tabGroups.update(existingGroups[0].id, { title: input.value });
      }
    },
    /**
     * Updates the rule color and applies it to the tab group.
     * @param {Event} event - Input event.
     */
    color: async ({ target: input }) => {
      updateRuleProperty(this.groupRule.id, "color", input.value);
      const existingGroups = await getGroupsByTitle(this.groupRule.ctmGroupTitle);
      this.groupRule.color = input.value;
      if (existingGroups.length > 0) {
        chrome.tabGroups.update(existingGroups[0].id, { color: input.value });
      }
    },
  };

  /**
   * Toggles the rule's enabled state.
   * @param {Event} event - Checkbox event.
   */
  toggleGroupRule({ target: checkbox }) {
    updateRuleProperty(this.groupRule.id, "enabled", checkbox.checked);
  }

  /**
   * Renders the group rule element.
   * @returns {TemplateResult} The rendered template.
   */
  render() {
    const matchedPatterns = Object.keys(this.groupRule.urlMatches).join(",");
    return html`
      <input
        type="checkbox"
        class="toggle_rule"
        @change=${this.toggleGroupRule.bind(this)}
      />
      <rule-details>
        <div class="left-column">
          <label>
            <span>${i18n("name")}:</span>
            <input
              type="text"
              .value=${this.groupRule.name}
              @change=${this.updateRuleProp.name}
            />
          </label>
          <label>
            <span>${i18n("group")}:</span>
            <input
              type="text"
              value=${this.groupRule.ctmGroupTitle || "auto"}
              @change=${this.updateRuleProp.ctmGroupName}
            />
          </label>
          <label>
            <span>${i18n("priority")}:</span>
            <input
              type="number"
              .value=${this.groupRule.priority}
              @change=${this.updateRuleProp.priority}
            />
          </label>
        </div>
        <div class="center-column"></div>
        <div class="right-column">
          <div>
            <span>${i18n("matches")}:</span>
            <var title=${matchedPatterns}>${matchedPatterns.slice(0, 10)}</var>
          </div>
          <label>
            <span>${i18n("color")}:</span>
            <select
              id="color_select"
              .value=${this.groupRule.color}
              @change=${this.updateRuleProp.color}
            >
              ${colorOptions.map(
                (color) =>
                  html`<option value="${color}">${color}</option>`
              )}
            </select>
          </label>
          <div style="justify-content: end">
            <vt-icon
              ico="edit"
              title=${i18n("edit_rule")}
              class="edit-icon"
              @click=${this.editGroupRule.bind(this)}
            ></vt-icon>
            <vt-icon
              ico="delete"
              title=${i18n("delete_rule")}
              class="delete-icon"
              @click=${this.deleteGroupRule.bind(this)}
            ></vt-icon>
          </div>
        </div>
      </rule-details>
    `;
  }

  connectedCallback() {
    this.id = this.groupRule.id;
    this.replaceChildren(this.render());
    if (this.groupRule.enabled) {
      this.firstElementChild.checked = true;
    }
  }
}
customElements.define("tabgroup-rule", GroupRuleElement);

/**
 * Updates the UI with the provided group rule.
 * @param {Object} rule - The group rule to update.
 */
async function updateRuleUI(rule) {
  const element = new GroupRuleElement(rule);
  const existing = document.querySelector("#r" + rule.id);
  if (existing) {
    existing.replaceWith(element);
  } else {
    document.body.appendChild(element);
  }
  const store = await getStore("autoGroupingOn");
  if (!store.autoGroupingOn) {
    await new Promise((resolve) => setTimeout(resolve, 2000));
    chrome.runtime.sendMessage({
      msg: "toggle_auto_grouping",
      autoGroupingOn: true,
    });
  }
}

let ruleListContainer = null;

/**
 * Custom element for the rule list.
 */
class RuleList extends HTMLElement {
  constructor() {
    super();
    ruleListContainer = this;
  }
  render(rules) {
    return rules.map((rule) => new GroupRuleElement(rule));
  }
  async connectedCallback() {
    const rules = await fetchAllRules();
    this.replaceChildren(...this.render(rules));
  }
}
customElements.define("tabgroup-rule-list", RuleList);

/**
 * Dialog for creating/editing rules.
 */
class RuleEditorDialogElement extends HTMLDialogElement {
  constructor(initialRule) {
    super();
    this.rule = { ...initialRule };
  }
  setInputValue() {
    for (const key in this.rule.urlMatches) {
      this.querySelector(`input[value="${key}"]`).checked = true;
      this.querySelector(`input[name="${key}"]`).value =
        this.rule.urlMatches[key].join(",");
    }
    this.querySelector(
      `input[value="${this.rule.color}"]`
    ).checked = true;
  }
  async createRule() {
    const newRule = {
      ...this.rule,
      urlMatches: { ...this.rule.urlMatches },
      titleIncludes: [...this.rule.titleIncludes],
    };
    try {
      await saveRule(newRule);
      await updateRuleUI(structuredClone(newRule));
      this.remove();
    } catch (error) {
      console.error(error);
      document.body.appendChild(new ErrorDisplay(error));
    }
  }
  removeMatchTitle(event) {
    const title = event.target.closest("li").textContent.trim();
    const index = this.rule.titleIncludes.indexOf(title);
    if (index !== -1) {
      this.rule.titleIncludes.splice(index, 1);
    }
    if (!event.target.closest("vt-icon")) {
      const input =
        event.currentTarget.previousElementSibling.previousElementSibling;
      input.value = title;
    }
  }
  addMatchTitle(event) {
    if (event.code === "Enter") {
      this.rule.titleIncludes.push(event.target.value);
      event.target.value = "";
    }
  }
  onUrlMatchChange(event) {
    const target = event.target;
    if (target.type === "checkbox") {
      const handle = (input) => {
        input.disabled = target.checked;
        if (target.checked) {
          delete this.rule.urlMatches[input.value];
        } else if (input.checked) {
          this.rule.urlMatches[input.value] =
            input.parentElement.nextElementSibling.value.trim();
        }
      };
      if (
        target.value === "two_path_segment" ||
        target.value === "domain"
      ) {
        const input =
          target.closest("li").previousElementSibling.firstElementChild
            .firstElementChild;
        handle(input);
      }
      if (target.value === "ctm_match_pattern") {
        for (const input of event.currentTarget.querySelectorAll(
          'input[name="url-match"]'
        )) {
          if (input !== target) handle(input);
        }
      }
      if (target.checked) {
        const value = target.parentElement.nextElementSibling.value.trim();
        this.rule.urlMatches[target.value] = value ? value.split(",") : [];
      } else {
        delete this.rule.urlMatches[target.value];
      }
    } else {
      const checkbox = target.previousElementSibling.firstElementChild;
      if (checkbox.checked) {
        this.rule.urlMatches[target.name] = target.value.trim()
          ? target.value.split(",")
          : [];
      }
    }
  }
  onColorChange(event) {
    this.rule.color = event.target.value;
  }
  render(tabProcessor) {
    const chipItem = (text) =>
      html`<li class="chip-item"><span>${text}</span><vt-icon ico="close" title="remove"></vt-icon></li>`;
    const colorOption = (color) =>
      html`<label style="--clr:${color}">
        <input type="radio" name="group-color" value=${color} hidden />
      </label>`;
    const dataListOption = (value) =>
      html`<option value=${value}></option>`;

    return html`<vt-icon
        ico="close-circle"
        class="close-btn"
        @click=${this.remove}
      ></vt-icon>
      <label>
        <span>${i18n("rule_name")}</span>
        <input type="text" .value=${this.rule.name} />
      </label>
      <section class="title-match" style="margin-top:0.8em">
        <label>
          <span><vt-icon ico="title"></vt-icon> ${i18n("tab_title_includes")}</span>
          <input
            type="text"
            list="tabTitles"
            @keyup=${this.addMatchTitle}
          />
        </label>
        <datalist id="tabTitles">
          ${tabProcessor.titleWords().map(dataListOption)}
        </datalist>
        <ul class="chip-list" @click=${this.removeMatchTitle}>
          ${this.rule.titleIncludes.map(chipItem)}
        </ul>
      </section>
      <section class="url-match">
        <header>
          <vt-icon ico="route"></vt-icon>
          <span>${i18n("url_match")}</span>
        </header>
        <ul @change=${this.onUrlMatchChange}>
          <li>
            <label>
              <input
                type="checkbox"
                name="url-match"
                value="top_domain"
              />
              ${i18n("top_domain")}
            </label>
            <input
              type="email"
              name="top_domain"
              list="top_domains"
              placeholder=${i18n("enter_selected_top_domains")}
              multiple
            />
            <datalist id="top_domains">
              ${tabProcessor.topDomains().map(dataListOption)}
            </datalist>
          </li>
          <li>
            <label>
              <input
                type="checkbox"
                name="url-match"
                value="domain"
              />
              ${i18n("domain")}
            </label>
            <input
              type="email"
              name="domain"
              list="domains"
              placeholder=${i18n("enter_selected_domains")}
              multiple
            />
            <datalist id="domains">
              ${tabProcessor.domains().map(dataListOption)}
            </datalist>
          </li>
          <li>
            <label>
              <input
                type="checkbox"
                name="url-match"
                value="first_path_segment"
              />
              ${i18n("first_path_segment")}
            </label>
            <input
              type="email"
              name="first_path_segment"
              list="first_path_segments"
              placeholder=${i18n("selected_first_path")}
              multiple
            />
            <datalist id="first_path_segments">
              ${tabProcessor.firstPathSegments().map(dataListOption)}
            </datalist>
          </li>
          <li>
            <label>
              <input
                type="checkbox"
                name="url-match"
                value="two_path_segment"
              />
              ${i18n("two_path_segment")}
            </label>
            <input
              type="email"
              name="two_path_segment"
              list="two_path_segments"
              placeholder=${i18n("selected_first_two_path")}
              multiple
            />
            <datalist id="two-path-segments">
              ${tabProcessor.twoPathSegments().map(dataListOption)}
            </datalist>
          </li>
          <li>
            <label>
              <input
                type="checkbox"
                name="url-match"
                value="ctm_match_pattern"
              />
              <span>
                ${i18n("custom_url")}
                <a href="https://developer.chrome.com/docs/extensions/develop/concepts/match-patterns">
                  ${i18n("match_patterns")}
                </a>
              </span>
            </label>
            <input
              type="email"
              name="ctm_match_pattern"
              placeholder="https://*/foo*,http://google.es/*"
            />
          </li>
        </ul>
      </section>
      <section class="rule-row">
        <label>
          <span>${i18n("custom_tabgroup_title")}</span>
          <input
            type="text"
            .value=${this.rule.ctmGroupTitle}
            placeholder="(optional) custom title"
            style="width:80%"
          />
        </label>
        <label>
          <span>${i18n("priority")}</span>
          <input type="number" .value=${this.rule.priority} />
        </label>
      </section>
      <section>
        <span style="margin-bottom:0.4em"
          ><vt-icon ico="color"></vt-icon> ${i18n("color")}</span
        >
        <div class="color-pot" @change=${this.onColorChange}>
          ${colorOptions.map(colorOption)}
        </div>
      </section>
      <button @click=${this.createRule}>${i18n("create_rule")}</button>`;
  }
  async connectedCallback() {
    this.id = "create-rule-dialog";
    const tabProcessor = new TabUrlProcessor();
    await tabProcessor.getUrls();
    this.replaceChildren(this.render(tabProcessor));
    this.showModal();
    this.setInputValue();
  }
}
customElements.define("create-rule-dialog", RuleEditorDialogElement, { extends: "dialog" });

import styles from "./tabgroup-rules-GCDCIF3A.css" with { type: "css" };

/**
 * Dialog wrapper for tab group rules.
 */
class TabGroupRuleDialog extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [styles];
  }
  showRuleCreator() {
    const ruleEditor = new RuleEditorDialogElement();
    this.shadowRoot.firstElementChild.appendChild(ruleEditor);
  }
  render() {
    return [
      html`<header>
        <vt-icon ico="close-circle" class="close-btn" @click=${this.remove}></vt-icon>
        <span><vt-icon ico="group-rule"></vt-icon> ${i18n("tabgroup_rules")}</span>
        <button @click=${this.showRuleCreator}><vt-icon ico="plus"></vt-icon> ${i18n("add_rule")}</button>
      </header>`,
      new RuleList(),
    ];
  }
  connectedCallback() {
    const dialog = document.createElement("dialog");
    dialog.append(...this.render());
    this.shadowRoot.appendChild(dialog);
    dialog.showModal();
  }
}
customElements.define("tabgroup-rule-dialog", TabGroupRuleDialog);

export { TabGroupRuleDialog };
