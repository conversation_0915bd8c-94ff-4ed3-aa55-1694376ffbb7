/**
 * TabVertikal Chrome Extension - Main Entry Point
 * A comprehensive tab management extension with advanced features for organizing,
 * searching, grouping, and managing browser tabs across multiple windows.
 *
 * @fileoverview Main application entry point with global utilities, keyboard shortcuts,
 * and core tab management functionality.
 */

// Import core utilities and components from consolidated chrome utils
import {
  queryTabs,
  createTab,
  removeTabs,
  TAB_GROUP_COLORS as groupColors,
  html as templateLiteral,
  withGlobalSetup,
  withI18n,
  checkAutoGroupRule,
  setSetting,
} from "./chrome-utils.js";

/**
 * Global utility setup for DOM, events, i18n, storage, and notifications.
 * Provides commonly used functions as global utilities for the extension.
 */
const globalSetup = withGlobalSetup((i18nContext) => {
  /**
   * Get element by ID.
   * @type {(id: string) => HTMLElement}
   */
  globalThis.getElementById = document.getElementById.bind(document);

  /**
   * Fire a custom event on an element.
   * @type {(el: HTMLElement, event: string, detail?: any) => void}
   */
  globalThis.fireEvent = (element, eventName, detail) =>
    element.dispatchEvent(detail ? new CustomEvent(eventName, { detail }) : new CustomEvent(eventName));

  /**
   * Add event listener.
   * @type {(el: HTMLElement, event: string, handler: EventListener) => void}
   */
  globalThis.addEvent = (element, eventName, handler) => element.addEventListener(eventName, handler);

  /**
   * Add one-time event listener.
   * @type {(el: HTMLElement, event: string, handler: EventListener) => void}
   */
  globalThis.addEventOnce = (element, eventName, handler) => element.addEventListener(eventName, handler, { once: true });

  /**
   * Query selector shortcut.
   * @type {(selector: string, root?: Document|HTMLElement) => HTMLElement}
   */
  globalThis.querySelector = (selector, root) => (root || document).querySelector(selector);

  /**
   * Internationalization message getter.
   * @type {(msg: string) => string}
   */
  globalThis.i18n = chrome.i18n.getMessage.bind(i18nContext);

  /**
   * Chrome local storage get/set.
   */
  globalThis.getStore = chrome.storage.local.get.bind(chrome.storage.local);
  globalThis.setStore = chrome.storage.local.set.bind(chrome.storage.local);

  /**
   * Toast notification for user feedback.
   * @type {(msg: string) => void}
   */
  const snackbar = getElementById("snackbar");
  globalThis.toast = (message) => {
    snackbar.hidden = false;
    snackbar.innerText = message;
    setTimeout(() => (snackbar.hidden = true), 5100);
  };
});
withI18n(globalSetup);

import icons from "/assets/icons.json" with { type: "json" };

/**
 * Custom element for displaying SVG icons.
 * Supports toggle state and checked state.
 */
/**
 * Custom element for displaying SVG icons.
 * Supports toggle state and checked state.
 * @element vt-icon
 */
class VtIconElement extends HTMLElement {
  /**
   * @param {string} [iconName]
   */
  constructor(iconName) {
    super();
    if (iconName) this.setAttribute("ico", iconName);
  }

  /**
   * Whether the icon is checked (for toggle icons).
   * @returns {boolean}
   */
  get checked() {
    return this._internals?.states.has("checked");
  }

  /**
   * Set checked state.
   * @param {boolean} value
   */
  set checked(value) {
    if (!this._internals) return;
    if (value) {
      this._internals.states.add("checked");
    } else {
      this._internals.states.delete("checked");
    }
  }

  /**
   * Set the icon by name.
   * @param {string} iconName
   */
  set ico(iconName) {
    if (this.firstElementChild) {
      this.firstElementChild.innerHTML = icons[iconName];
    }
  }

  /**
   * Render the SVG for the icon.
   * @param {string} iconName
   * @returns {string}
   */
  render(iconName) {
    return `<svg viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">${icons[iconName]}</svg>`;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  connectedCallback() {
    this.innerHTML = this.render(this.getAttribute("ico"));
    if (this.hasAttribute("toggle")) {
      this._internals = this.attachInternals();
      this.addEventListener("click", this.#onToggle.bind(this));
    }
  }

  /**
   * Toggle checked state and fire change event.
   * @private
   */
  #onToggle() {
    this.checked = !this.checked;
    this.dispatchEvent(new Event("change"));
  }
}
customElements?.define("vt-icon", VtIconElement);
/**
 * Set the color of a tab group element.
 * @param {HTMLElement} element - The element to style.
 * @param {string} color - The group color name.
 */
function setTabGroupColor(element, color) {
  element?.style.setProperty("--grp-clr", groupColors[color ?? "grey"]);
}

/**
 * Open Chrome's tab discard manager.
 */
function openTabDiscardManager() {
  chrome.tabs.create({ url: "chrome://discards/" });
}

/**
 * Open the extension's options page.
 */
function openOptionsPage() {
  chrome.runtime.openOptionsPage();
}

/**
 * Show the Tab Group Rule dialog.
 */
async function showTabGroupRuleDialog() {
  const { TabGroupRuleDialog } = await import("./tabgroup-rules-6PBAL65H.js");
  document.body.appendChild(new TabGroupRuleDialog());
}

/**
 * Group all ungrouped tabs, or notify if no rule exists.
 */
async function groupAllUngroupedTabs() {
  if (!(await checkAutoGroupRule()))
    return notify(i18n("create_autogroup_rule_first"), "error");
  await chrome.runtime.sendMessage("group_all_ungroup_tabs");
}

/**
 * Get the index of the currently active tab in the current window.
 * @returns {Promise<number>}
 */
async function getActiveTabIndex() {
  return (await queryTabs({ windowId: globalThis.windowId, active: true }))[0].index;
}

/**
 * Create a new tab at the current tab's index.
 */
async function createTabAtCurrentIndex() {
  createTab({ index: await getActiveTabIndex() });
}

/**
 * Create a new tab after the current tab.
 */
async function createTabAfterCurrent() {
  createTab({ index: (await getActiveTabIndex()) + 1 });
}

/**
 * Close all tabs above the current tab.
 */
async function closeTabsAbove() {
  const currentIndex = await getActiveTabIndex();
  const tabsToClose = (await queryTabs({ windowId })).slice(0, currentIndex).map(tab => tab.id);
  chrome.tabs.remove(tabsToClose).catch(console.error);
}

/**
 * Close all tabs below the current tab.
 */
async function closeTabsBelow() {
  const currentIndex = await getActiveTabIndex();
  const tabsToClose = (await queryTabs({ windowId })).slice(currentIndex).map(tab => tab.id);
  chrome.tabs.remove(tabsToClose).catch(console.error);
}

/**
 * Close all tabs except the active one.
 */
async function closeOtherTabs() {
  const tabsToClose = (await queryTabs({ windowId })).filter(tab => !tab.active).map(tab => tab.id);
  chrome.tabs.remove(tabsToClose).catch(console.error);
}

/**
 * Get the ID of the currently active tab in the current window.
 * @returns {Promise<number>}
 */
async function getActiveTabId() {
  return (await queryTabs({ windowId: globalThis.windowId, active: true }))[0].id;
}

/**
 * Duplicate the currently active tab.
 */
async function duplicateActiveTab() {
  chrome.tabs.duplicate(await getActiveTabId());
}

/**
 * Discard (suspend) the currently active tab.
 */
async function discardActiveTab() {
  chrome.tabs.discard(await getActiveTabId());
}

/**
 * Show the Tab Group selection dialog for a tab.
 * @param {number} [tabId]
 */
async function showTabGroupSelect(tabId) {
  tabId ??= await getActiveTabId();
  const { TabgroupSelect } = await import("./tabgroup-select-KETNIX4M.js");
  document.body.appendChild(new TabgroupSelect([tabId]));
}

/**
 * Show the Window selection dialog for a tab.
 * @param {number} [tabId]
 */
async function showWindowSelect(tabId) {
  tabId ??= await getActiveTabId();
  const { WindowSelect } = await import("./chunk-EC5AYDH5.js");
  document.body.appendChild(new WindowSelect([tabId]));
}

/**
 * Remove duplicate tabs in the current window.
 */
async function removeDuplicateTabs() {
  const tabs = await chrome.tabs.query({ windowId: globalThis.windowId });
  const urlToId = {};
  const duplicateIds = new Set();
  tabs.reduce((acc, tab) => {
    if (acc[tab.url]) {
      duplicateIds.add(tab.id);
    } else {
      acc[tab.url] = tab.id;
    }
    return acc;
  }, urlToId);
  if (duplicateIds.size !== 0) {
    chrome.tabs.remove([...duplicateIds]).catch(console.error);
  }
}
/**
 * Remove all tabs from a specific domain.
 * @param {Event} event
 */
function removeTabsByDomain({ currentTarget }) {
  const domain = currentTarget.dataset.domain;
  if (!domain) return;
  const idsToRemove = [];
  querySelector("tab-container").tabs.filter(tab =>
    new URL(tab.url).host !== domain ? true : (idsToRemove.push(tab.id), false)
  );
  removeTabs(idsToRemove);
  currentTarget.remove();
}

/**
 * Suspend (discard) all tabs except the active one in the current window.
 */
function suspendOtherTabs() {
  chrome.tabs
    .query({ currentWindow: true })
    .then(tabs =>
      tabs.forEach(({ active, id }) => {
        if (!active) chrome.tabs.discard(id);
      })
    )
    .catch(console.error);
}

/**
 * Handle paste event for URLs: activate if open, otherwise open in new tab.
 * @param {ClipboardEvent} event
 */
async function handlePasteUrl(event) {
  const url =
    event.clipboardData.getData("text/uri-list") ||
    event.clipboardData.getData("text/plain");
  if (url.startsWith("http") && URL.canParse(url)) {
    const existingTab = (await queryTabs({ windowId, url }))[0];
    if (existingTab) {
      chrome.tabs.update(existingTab.id, { active: true });
    } else {
      createTab({ url, index: await getActiveTabIndex() });
    }
    event.preventDefault();
  }
}

/**
 * Save a tab group's color to persistent storage.
 * @param {{title: string, color: string}} group
 */
async function saveTabGroupColor(group) {
  try {
    const store = (await getStore("tabGroups")).tabGroups || {};
    if (store[group.title]) return;
    store[group.title] = group.color;
    await setStore({ tabGroups: store });
  } catch (err) {
    console.error(err);
  }
}

/**
 * Keyboard shortcut handler for the extension.
 */
class KeyboardShortcuts {
  constructor() {
    addEventListener("keydown", this.keyUpListener.bind(this));
  }

  altKeys = {
    KeyN: createTabAfterCurrent,
    KeyD: duplicateActiveTab,
    KeyH: discardActiveTab,
    KeyG: showTabGroupSelect,
    KeyM: showWindowSelect,
  };

  ctrlShiftKeys = {
    KeyH: suspendOtherTabs,
    KeyE: async () => {
      const { ImportExportDialog } = await import("./import-export-dialog-G6WE4YFT.js");
      this.importExportDialog = new ImportExportDialog();
      querySelector("window-bar").shadowRoot.appendChild(this.importExportDialog);
    },
    keyQ: closeTabsAbove,
    KeyF: () =>
      querySelector('input[type="search"]', querySelector("window-bar").shadowRoot).focus(),
  };

  ctrlAltKeys = {
    KeyC: () => {
      const tabContainer = querySelector("tab-container");
      tabContainer.toggleAttribute("compact");
      setSetting({ compactMode: tabContainer.hasAttribute("compact") });
    },
    KeyD: removeDuplicateTabs,
    KeyO: openOptionsPage,
    KeyN: createTabAtCurrentIndex,
    KeyR: showTabGroupRuleDialog,
    keyS: openTabDiscardManager,
    KeyW: closeTabsBelow,
    KeyQ: closeOtherTabs,
  };

  /**
   * Main keyup event handler for shortcuts.
   * @param {KeyboardEvent} event
   */
  keyUpListener(event) {
    if (event.ctrlKey) {
      if (event.shiftKey) {
        this.ctrlShiftKeys[event.code]?.();
      } else if (event.altKey || event.metaKey) {
        this.ctrlAltKeys[event.code]?.();
      }
    } else if (event.altKey || event.metaKey) {
      this.altKeys[event.code]?.();
    }
  }
}

// Initialize keyboard shortcuts after 1 second.
setTimeout(() => new KeyboardShortcuts(), 1000);
import Rt from "./alert-box-QPHCQPTR.css" with { type: "css" };
/**
 * AlertBoxElement - Custom element for displaying alert notifications.
 * Shows a popover with a message and style (success/error).
 * @class
 * @extends HTMLElement
 */
class AlertBoxElement extends HTMLElement {
  /**
   * Initializes the alert box and attaches shadow DOM.
   */
  constructor() {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [Rt];
  }

  /**
   * Show the alert box with a message and type.
   * @param {string} message - The message to display.
   * @param {string} [type="success"] - The type of alert ("success" or "error").
   */
  show = (message, type = "success") => {
    this.box.className = type;
    this.box.children[1].textContent = message;
    this.showPopover();
    setTimeout(() => this.hidePopover(), 4100);
  };

  /**
   * Render the alert box HTML.
   * @returns {string}
   */
  render() {
    return `<div>
      <svg width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="white" d='M10,17L5,12L6.41,10.58L10,14.17L17.59,6.58L19,8M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z' /></svg>
      <span class="notice-txt"></span>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="white" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" /></svg>
    </div>`;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  connectedCallback() {
    this.id = "alert-box";
    this.setAttribute("popover", "");
    this.shadowRoot.innerHTML = this.render();
    this.box = this.shadowRoot.firstElementChild;
    this.box.lastElementChild.addEventListener("click", () => this.hidePopover());
  }
}
customElements.define("alert-box", AlertBoxElement);

// Singleton instance for global notifications
const globalAlertBox = new AlertBoxElement();
document.body.appendChild(globalAlertBox);
globalThis.notify = globalAlertBox.show;
/**
 * Extension manifest details for error reporting.
 * @type {{ version: string, short_name: string, update_url: string }}
 */
const {
  version: extensionVersion,
  short_name: extensionShortName,
  update_url: extensionUpdateUrl,
} = chrome.runtime.getManifest();

/**
 * Indicates if the extension is in development mode (no update URL).
 * @type {boolean}
 */
const isDevelopmentMode = !extensionUpdateUrl;

/**
 * Reports errors to a remote bug collector service.
 * @param {ErrorEvent|PromiseRejectionEvent} errorEvent
 */
function reportError(errorEvent) {
  if (isDevelopmentMode) return console.error(errorEvent);
  const endpoint = "https://bug-collector.noterail.workers.dev/collect-bug";
  const payload = {
    id: 1,
    extId: chrome.runtime.id,
    extName: extensionShortName,
    extVersion: extensionVersion,
    message: errorEvent.message ?? errorEvent.reason,
    stack: errorEvent.stack,
    browserOs: navigator.userAgent,
    catchedAt: new Date().toISOString(),
  };
  const request = new Request(endpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(payload),
  });
  fetch(request)
    .then((response) => response.text())
    .then((text) => console.log(text))
    .catch((err) => console.log(err));
}

// Attach global error handlers in production mode.
if (!isDevelopmentMode) {
  self.addEventListener("error", reportError);
  self.addEventListener("unhandledrejection", reportError);
  // Proxy console.error to also report errors.
  const errorProxyHandler = {
    apply: function (target, thisArg, argumentsList) {
      reportError(argumentsList[0]);
      return target.call(thisArg, ...argumentsList);
    },
  };
  console.error = new Proxy(console.error, errorProxyHandler);
}

/**
 * FilterTabsElement - Custom element for filtering tabs by domain.
 * Displays a list of domains for the current window's tabs.
 * @class
 * @extends HTMLElement
 */
class FilterTabsElement extends HTMLElement {
  /**
   * Initializes the filter tabs element.
   */
  constructor() {
    super();
    /** @type {string[]} */
    this.domains = [];
  }

  /**
   * Fetches unique domains from all tabs in the current window.
   * @returns {Promise<string[]>}
   */
  async fetchDomains() {
    const tabs = await r({ windowId: globalThis.windowId });
    const domains = tabs.map(tab => new URL(tab.url).host);
    return [...new Set(domains)];
  }

  /**
   * Renders a domain list item.
   * @param {string} domain
   * @returns {any}
   */
  domainItem = (domain) => c`<li data-domain=${domain} @click=${It}>
    <span>${domain}</span>
    <vt-icon ico="delete-cross"></vt-icon>
  </li>`;

  /**
   * Renders the domain list.
   * @param {string[]} domains
   * @returns {any[]}
   */
  render(domains) {
    return domains.map(this.domainItem);
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  connectedCallback() {
    this.id = "filter-tabs";
    this.style.top = "13em";
    this.setAttribute("popover", "");
    $on(this, "toggle", (event) => {
      if (event.newState === "open") {
        this.fetchDomains().then((domains) =>
          this.replaceChildren(...this.render(domains))
        );
      }
    });
    this.showPopover();
  }
}
customElements.define("filter-tabs", FilterTabsElement);
/**
 * TabSortUtils - Utility class for sorting browser tabs by various criteria.
 * Provides static comparator functions and a main sorting dispatcher.
 */
class TabSortUtils {
  /**
   * Sort tabs by domain name (A-Z).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byDomainAZ(a, b) {
    const domainA = new URL(a.url).host;
    const domainB = new URL(b.url).host;
    return domainA.localeCompare(domainB);
  }

  /**
   * Sort tabs by domain name (Z-A).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byDomainZA(a, b) {
    const domainA = new URL(a.url).host;
    const domainB = new URL(b.url).host;
    return domainB.localeCompare(domainA);
  }

  /**
   * Sort tabs by URL (A-Z).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byUrlAZ(a, b) {
    return a.url.localeCompare(b.url);
  }

  /**
   * Sort tabs by URL (Z-A).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byUrlZA(a, b) {
    return b.url.localeCompare(a.url);
  }

  /**
   * Sort tabs by title (A-Z).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byTitleAZ(a, b) {
    return a.title.localeCompare(b.title);
  }

  /**
   * Sort tabs by title (Z-A).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byTitleZA(a, b) {
    return b.title.localeCompare(a.title);
  }

  /**
   * Sort tabs by last accessed time (oldest to newest).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byTimeAZ(a, b) {
    return (a.lastAccessed ?? 0) - (b.lastAccessed ?? 0);
  }

  /**
   * Sort tabs by last accessed time (newest to oldest).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byTimeZA(a, b) {
    return (b.lastAccessed ?? 0) - (a.lastAccessed ?? 0);
  }

  /**
   * Placeholder for group sorting (not implemented).
   * @returns {number}
   */
  static byGroupAZ() {
    return 0;
  }

  /**
   * Sorts and reorders tabs in the browser window.
   * @param {chrome.tabs.Tab[]} tabs - Array of tab objects to sort.
   * @param {string} sortType - Sorting criteria identifier.
   */
  static async sortTabs(tabs, sortType) {
    switch (sortType) {
      case "domainAZ":
        tabs.sort(TabSortUtils.byDomainAZ);
        break;
      case "domainZA":
        tabs.sort(TabSortUtils.byDomainZA);
        break;
      case "titleAZ":
        tabs.sort(TabSortUtils.byTitleAZ);
        break;
      case "titleZA":
        tabs.sort(TabSortUtils.byTitleZA);
        break;
      case "timeAZ":
        tabs.sort(TabSortUtils.byTimeAZ);
        break;
      case "timeZA":
        tabs.sort(TabSortUtils.byTimeZA);
        break;
      case "reverse":
        tabs.reverse();
        break;
      // Add more sort types as needed
    }
    // Move each tab to its new index
    const movePromises = tabs.map((tab, idx) =>
      chrome.tabs.move(tab.id, { index: idx })
    );
    try {
      await Promise.all(movePromises);
    } catch (err) {
      notify("cannot sort tabs", "error");
    }
  }
}

/**
 * SortTabsElement - Custom element for displaying and handling tab sorting options.
 * Renders a popover menu with sorting choices and applies the selected sort.
 * @element sort-tabs
 */
class SortTabsElement extends HTMLElement {
  constructor() {
    super();
  }

  /**
   * Handles click events on sorting options and triggers tab sorting.
   * @param {Event} event
   */
  async handleSortClick({ target }) {
    // Fetch all tabs in the current window
    const tabs = await r({ windowId: globalThis.windowId });
    // Determine sort type from clicked list item's id
    const sortType = target.closest("li").id;
    await TabSortUtils.sortTabs(tabs, sortType);
  }

  /**
   * Renders the sorting options menu.
   * @returns {any}
   */
  render() {
    return c`
      <li id="domainAZ">
        <vt-icon ico="domain"></vt-icon>
        <span>Domain</span>
        <vt-icon ico="asc"></vt-icon>
      </li>
      <li id="domainZA">
        <vt-icon ico="domain"></vt-icon>
        <span>Domain</span>
        <vt-icon ico="desc"></vt-icon>
      </li>
      <li id="titleAZ">
        <vt-icon ico="web"></vt-icon>
        <span>Title</span>
        <vt-icon ico="asc"></vt-icon>
      </li>
      <li id="titleZA">
        <vt-icon ico="web"></vt-icon>
        <span>Title</span>
        <vt-icon ico="desc"></vt-icon>
      </li>
      <li id="timeAZ">
        <vt-icon ico="recent"></vt-icon>
        <span>${i18n("recent_used")}</span>
        <vt-icon ico="asc"></vt-icon>
      </li>
      <li id="timeZA">
        <vt-icon ico="recent"></vt-icon>
        <span>${i18n("recent_used")}</span>
        <vt-icon ico="desc"></vt-icon>
      </li>
      <li id="reverse">
        <vt-icon ico="reverse"></vt-icon>
        <span>${i18n("reverse")}</span>
        <vt-icon ico="reverse"></vt-icon>
      </li>
    `;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  connectedCallback() {
    this.id = "sort-tabs";
    this.style.top = "6.5em";
    this.setAttribute("popover", "");
    this.replaceChildren(this.render());
    this.showPopover();
    $on(this, "click", this.handleSortClick);
  }
}
customElements.define("sort-tabs", SortTabsElement);
/**
 * MoreMenuElement - Custom element for the "More" menu popup.
 * Provides actions for tab creation, sorting, filtering, settings, and more.
 * @class
 * @extends HTMLElement
 */
class MoreMenuElement extends HTMLElement {
  /**
   * Reference to the tab container element.
   * @type {HTMLElement|null}
   */
  tabContainer = null;

  /**
   * Reference to the filter tabs popover element.
   * @type {FilterTabsElement|null}
   */
  filterTabsPopover = null;

  /**
   * Reference to the sort tabs popover element.
   * @type {SortTabsElement|null}
   */
  sortTabsPopover = null;

  /**
   * Reference to the import/export dialog element.
   * @type {HTMLElement|null}
   */
  importExportDialog = null;

  constructor() {
    super();
  }

  /**
   * Toggles compact mode for the tab container and persists the setting.
   */
  toggleCompactMode() {
    if (!this.tabContainer) {
      this.tabContainer = document.querySelector("tab-container");
    }
    if (this.tabContainer) {
      this.tabContainer.toggleAttribute("compact");
      setSetting({ compactMode: this.tabContainer.hasAttribute("compact") });
    }
  }

  /**
   * Opens Chrome's side panel settings in a new tab.
   */
  openSidePanelSettings() {
    chrome.tabs.create({
      url: "chrome://settings/appearance#:~:text=side%20panel",
    });
  }

  /**
   * Shows the filter tabs popover, creating it if necessary.
   * @param {Event} event
   */
  showFilterTabsPopover({ currentTarget }) {
    if (this.filterTabsPopover) {
      this.filterTabsPopover.showPopover();
      return;
    }
    this.filterTabsPopover = new FilterTabsElement();
    currentTarget.appendChild(this.filterTabsPopover);
  }

  /**
   * Shows the sort tabs popover, creating it if necessary.
   * @param {Event} event
   */
  showSortTabsPopover({ currentTarget }) {
    if (this.sortTabsPopover) {
      this.sortTabsPopover.showPopover();
      return;
    }
    this.sortTabsPopover = new SortTabsElement();
    currentTarget.appendChild(this.sortTabsPopover);
  }

  /**
   * Shows the import/export dialog popover, creating it if necessary.
   * @param {Event} event
   */
  async showImportExportPopover({ currentTarget }) {
    if (this.importExportDialog) {
      this.importExportDialog.showPopover();
      return;
    }
    const { ImportExportDialog } = await import("./import-export-dialog-G6WE4YFT.js");
    this.importExportDialog = new ImportExportDialog();
    currentTarget.appendChild(this.importExportDialog);
  }

  /**
   * Renders the More menu popup with all available actions.
   * @returns {any}
   */
  render() {
    return c`
      <more-menu-popup id="more-menu" style="right:2px" popover>
        <li @click=${createTabAfterCurrent}>
          <vt-icon ico="plus"></vt-icon> <span>${i18n("create_tab")}</span> <kbd>Alt+N</kbd>
        </li>
        <li class="sort-tabs-btn" @click=${this.showSortTabsPopover.bind(this)}>
          <vt-icon ico="sort"></vt-icon> <span>${i18n("sort")}</span>
        </li>
        <li @click=${showTabGroupRuleDialog}>
          <vt-icon ico="group"></vt-icon> <span>${i18n("auto_group_rules")}</span><kbd>Ctrl+Alt+R</kbd>
        </li>
        <li @click=${groupAllUngroupedTabs}>
          <vt-icon ico="group-tabs"></vt-icon> <span>${i18n("auto_group_all_ungroup_tabs")}</span>
        </li>
        <li @click=${removeDuplicateTabs}>
          <vt-icon ico="duplicate"></vt-icon> <span>${i18n("remove_duplicate_tabs")}</span><kbd>Alt+D</kbd>
        </li>
        <li class="filter-tabs-btn" @click=${this.showFilterTabsPopover.bind(this)}>
          <vt-icon ico="filter"></vt-icon> <span>${i18n("filter")}</span>
        </li>
        <li @click=${openTabDiscardManager}>
          <vt-icon ico="suspend"></vt-icon> <span>${i18n("suspend_manager")}</span><kbd>Ctrl+Alt+S</kbd>
        </li>
        <li @click=${suspendOtherTabs}>
          <vt-icon ico="sleep"></vt-icon> <span>${i18n("suspend_other_tabs")}</span> <kbd>Ctrl+Shift+H</kbd>
        </li>
        <li @click=${openOptionsPage}>
          <vt-icon ico="settings"></vt-icon> <span>${i18n("settings")}</span><kbd>Ctrl+Alt+O</kbd>
        </li>
        <li @click=${this.toggleCompactMode.bind(this)}>
          <vt-icon ico="compact"></vt-icon> <span>${i18n("compact_mode")}</span><kbd>Ctrl+Alt+C</kbd>
        </li>
        <li @click=${this.openSidePanelSettings.bind(this)}>
          <vt-icon ico="panel-position"></vt-icon> <span>${i18n("panel_position")}</span>
        </li>
        <li @click=${this.showImportExportPopover.bind(this)}>
          <vt-icon ico="export"></vt-icon> <span>${i18n("import_export_tab")}</span><kbd>Ctrl+Shift+E</kbd>
        </li>
      </more-menu-popup>
      <button popovertarget="more-menu"><vt-icon ico="menu"></vt-icon></button>
    `;
  }

  /**
   * Lifecycle: called when element is added to the DOM.
   */
  connectedCallback() {
    this.replaceChildren(this.render());
  }
}

customElements.define("more-menu", MoreMenuElement);
/**
 * Constructs the good suffix table for the Boyer-Moore string search algorithm.
 * This table is used to determine how far the search window can be shifted when a mismatch occurs,
 * based on the matched suffix of the pattern (needle).
 *
 * @param {string} pattern - The pattern string for which to build the good suffix table.
 * @returns {number[]} An array of shift amounts for each suffix position in the pattern.
 */
function buildGoodSuffixTable(pattern) {
  /**
   * The resulting table of shift values for each suffix position.
   * Each entry represents how far the pattern can be shifted when a mismatch occurs at that position.
   * @type {number[]}
   */
  const goodSuffixShifts = [];
  const patternLength = pattern.length;

  // Iterate from the end of the pattern towards the start
  for (let suffixStart = patternLength - 1; suffixStart > 0; suffixStart--) {
    // The suffix to match (from current position to end)
    const suffix = pattern.slice(suffixStart);
    // The prefix to search for matching substrings
    const prefix = pattern.slice(0, suffixStart);
    const suffixLength = suffix.length;
    let shift = -1;

    // Search for the rightmost occurrence of the suffix in the prefix
    for (let j = prefix.length; j >= suffixLength; j--) {
      const window = prefix.slice(j - suffixLength, j);
      if (window === suffix) {
        // Found a matching substring; calculate the shift
        shift = patternLength - j;
        break;
      }
    }
    // If no match is found, stop building the table
    if (shift === -1) break;
    goodSuffixShifts.push(shift);
  }
  return goodSuffixShifts;
}
/**
 * Builds a bad character table for the Boyer-Moore string search algorithm.
 * This table maps each character in the pattern to its rightmost position.
 * @param {string} pattern - The search pattern.
 * @returns {Object.<string, number>} Character position mapping.
 */
function buildBadCharacterTable(pattern) {
  const charTable = {};
  const patternLength = pattern.length;
  for (let i = 0; i < patternLength; i++) {
    charTable[pattern[i]] = i;
  }
  return charTable;
}

/**
 * Boyer-Moore string search algorithm implementation.
 * Provides efficient string searching with both bad character and good suffix heuristics.
 */
class BoyerMooreSearcher {
  /**
   * @param {string} needle - The pattern to search for.
   */
  constructor(needle) {
    /** @type {string} The search pattern */
    this.needle = needle;
    /** @type {Object.<string, number>} Bad character table for quick character skips */
    this.badCharTable = buildBadCharacterTable(needle);
    /** @type {number[]} Good suffix table for pattern-based skips */
    this.goodSuffixTable = buildGoodSuffixTable(needle);
    /** @type {number} Length of the search pattern */
    this.count = needle.length;
  }

  /**
   * Gets the shift amount based on good suffix heuristic.
   * @param {number} mismatchIndex - Index where mismatch occurred.
   * @returns {number} Number of positions to shift.
   */
  getGoodShift(mismatchIndex) {
    let shift = this.goodSuffixTable[mismatchIndex];
    // Fallback to last entry or pattern length if no specific shift found
    shift ??= this.goodSuffixTable[this.goodSuffixTable.length - 1];
    shift ??= this.count;
    return shift;
  }

  /**
   * Searches for all occurrences of the pattern in the given text.
   * @param {string} text - The text to search in.
   * @returns {number[]} Array of starting positions where pattern was found.
   */
  searchAll(text) {
    const matches = [];
    let textIndex = 0;
    const maxIndex = text.length - this.count;

    while (textIndex <= maxIndex) {
      let patternIndex = this.count - 1;

      // Compare pattern from right to left
      while (
        this.needle[patternIndex] === text[textIndex + patternIndex] &&
        (patternIndex-- === 0 && matches.push(textIndex), patternIndex !== -1)
      );

      // Calculate shift using bad character heuristic
      const badCharShift = patternIndex - (this.badCharTable[text[textIndex + patternIndex]] ?? -1);
      let totalShift = badCharShift;

      // Use good suffix heuristic if we had a partial match
      if (patternIndex !== this.count - 1) {
        const goodSuffixShift = this.getGoodShift(patternIndex);
        totalShift = Math.max(badCharShift, goodSuffixShift);
      }

      textIndex += totalShift;
    }

    return matches;
  }
}
/**
 * SearchTabsElement - Custom element for searching and highlighting tabs.
 * Provides real-time search functionality with text highlighting using Boyer-Moore algorithm.
 * @element search-tabs
 */
class SearchTabsElement extends HTMLElement {
  /** @type {HTMLInputElement} The search input field */
  inputField;

  /** @type {Highlight} CSS Highlight API instance for text highlighting */
  highlighter;

  /** @type {HTMLElement} Reference to the tab container */
  tabContainer;

  constructor() {
    super();
    // Initialize CSS Highlight API for search result highlighting
    this.highlighter = new Highlight();
    CSS.highlights.set("search-highlight", this.highlighter);
  }

  /**
   * Highlights matching text ranges in the given text node.
   * @param {number[]} matchPositions - Array of match start positions.
   * @param {Text} textNode - The text node to highlight.
   */
  highlightMatchRange(matchPositions, textNode) {
    const searchLength = this.inputField.value.length;
    for (const position of matchPositions) {
      if (position + searchLength > textNode.length) continue;
      const range = new Range();
      range.setStart(textNode, position);
      range.setEnd(textNode, position + searchLength);
      this.highlighter.add(range);
    }
  }

  /**
   * Searches through all tabs and highlights matches in title and URL.
   * @param {BoyerMooreSearcher} searcher - The search algorithm instance.
   */
  searchTab(searcher) {
    this.highlighter.clear();
    for (const tabElement of this.tabContainer.shadowRoot.children) {
      const titleMatches = searcher.searchAll(tabElement.tab.title.toLowerCase());
      const urlMatches = searcher.searchAll(tabElement.tab.url.slice(8).toLowerCase());

      if (titleMatches.length === 0 && urlMatches.length === 0) {
        tabElement.hidden = true;
      } else {
        if (tabElement.hidden) {
          tabElement.hidden = false;
        }
        // Navigate to the text nodes for highlighting
        const textContainer = tabElement.lastElementChild.previousElementSibling.lastElementChild.firstElementChild;

        if (titleMatches.length > 0) {
          this.highlightMatchRange(titleMatches, textContainer.firstChild);
        }
        if (urlMatches.length > 0) {
          this.highlightMatchRange(urlMatches, textContainer.nextElementSibling.firstChild);
        }
      }
    }
  }

  /**
   * Handles search input events and triggers search or reset.
   * @param {{target: HTMLInputElement}} event - The input event.
   */
  searchQuery({ target }) {
    const query = target.value.toLowerCase();
    if (query) {
      const searcher = new BoyerMooreSearcher(query);
      this.searchTab(searcher);
    } else {
      this.reset();
    }
  }

  /**
   * Resets the search state, clearing highlights and showing all tabs.
   */
  reset() {
    this.highlighter.clear();
    for (const tabElement of this.tabContainer.shadowRoot.children) {
      if (tabElement.hidden) {
        tabElement.hidden = false;
      }
    }
  }

  /**
   * Focuses the search input field.
   */
  showSearchBox() {
    this.inputField.focus();
  }

  /**
   * Renders the search component HTML.
   * @returns {DocumentFragment} The rendered search interface.
   */
  render() {
    return templateLiteral`<search>
				<input
					type="search"
					placeholder="🔎 ${i18n("search_tabs")}"
					ref=${(element) => (this.inputField = element)}
					@input=${this.searchQuery.bind(this)} />
			</search>
			<vt-icon ico="search" @click=${this.showSearchBox.bind(this)}></vt-icon>`;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  connectedCallback() {
    this.style.position = "relative";
    this.replaceChildren(this.render());
    this.tabContainer = querySelector("tab-container");
  }
}
customElements.define("search-tabs", SearchTabsElement);
/**
 * WorkspaceTabsElement - Manages workspace creation, switching, and persistence.
 * Provides a UI for users to save and restore tab configurations across different workspaces.
 * @element workspace-tabs
 */
class WorkspaceTabsElement extends HTMLElement {
  /** @type {Array<{id: string, name: string}>} List of available workspaces */
  workspaces;

  /** @type {string} Currently active workspace ID */
  atvWorkspaceId;

  constructor() {
    super();
  }

  /**
   * Creates a new workspace with user input dialog.
   * @returns {HTMLDialogElement} The workspace creation dialog.
   */
  createWorkspace() {
    const dialog = new UpdateWorkspaceDialog();
    return (
      addEventListener(dialog, "create", async ({ detail: workspaceName }) => {
        const newWorkspace = {
          id: Math.random().toString(36).slice(2),
          name: workspaceName
        };
        const storage = (await getStore("workspaces")).workspaces ?? [];
        storage.push(newWorkspace);
        setStore({ workspaces: storage });
        this.workspaces.splice(0, 0, newWorkspace);
        this.switchWorkspace(newWorkspace.id);
        this.firstElementChild.hidePopover();
        querySelector(".workspace", this).textContent = newWorkspace.name;
        querySelector(`input[value="${newWorkspace.id}"]`, this).checked = true;
      }),
      this.firstElementChild.appendChild(dialog)
    );
  }

  /**
   * Switches to a different workspace, saving current state and restoring target state.
   * @param {string} workspaceId - The ID of the workspace to switch to.
   */
  async switchWorkspace(workspaceId) {
    // Save current workspace state
    const currentTabs = await queryTabs({ windowId: -2 });
    const currentGroups = new Map((await queryTabGroups({ windowId: -2 })).map((group) => [group.id, group.title]));

    setStore({
      [this.atvWorkspaceId]: currentTabs.map((tab) => ({
        url: tab.url,
        groupTitle: currentGroups[tab.groupId],
      })),
    });

    // Switch to new workspace
    this.atvWorkspaceId = workspaceId;
    setStore({ atvWorkspaceId: this.atvWorkspaceId });

    // Load target workspace state
    const targetWorkspaceData = (await getStore(workspaceId))[workspaceId] ?? [];
    if (targetWorkspaceData.length === 0) {
      return createTab({ index: 0, active: false });
    }

    // Close excess tabs if current workspace has more tabs
    if (currentTabs.length > targetWorkspaceData.length) {
      await removeTabs(currentTabs.slice(targetWorkspaceData.length).map((tab) => tab.id));
    }

    toast("Restoring...");

    const groupMap = {};
    const tabContainer = querySelector("tab-container");

    /**
     * Helper function to suspend a tab and update UI references.
     * @param {number} tabId - The ID of the tab to suspend.
     */
    async function suspendAndUpdateTab(tabId) {
      const { id: newId } = await chrome.tabs.discard(tabId);
      const tabElement = tabContainer.shadowRoot.getElementById(String(tabId));
      tabElement.id = newId;
      tabElement.tab.id = newId;
    }

    try {
      for (let index = 0; index < targetWorkspaceData.length; index++) {
        const targetTab = targetWorkspaceData[index];
        const { id: tabId } = currentTabs[index]
          ? await chrome.tabs.update(currentTabs[index].id, { url: targetTab.url })
          : await createTab({ url: targetTab.url, active: false });

        // Handle tab grouping
        if (targetTab.groupTitle) {
          if (groupMap[targetTab.groupTitle]) {
            await chrome.tabs.group({ tabIds: tabId, groupId: groupMap[targetTab.groupTitle] });
          } else {
            const groupId = await chrome.tabs.group({ tabIds: tabId });
            await chrome.tabGroups.update(groupId, {
              title: targetTab.groupTitle,
              color: "cyan",
            });
            groupMap[targetTab.groupTitle] = groupId;
          }
        }

        // Add delay and suspend tab to save memory
        await new Promise((resolve) => setTimeout(resolve, 1000));
        await suspendAndUpdateTab(tabId);
      }
      notify("Workspace switched");
    } catch (error) {
      console.error(error);
      document.body.appendChild(new ErrorDialog(error));
    }
  }

  /**
   * Handles workspace selection change events.
   * @param {Event} event - The change event from workspace selection.
   */
  onWorkspaceChange(event) {
    const selectedWorkspaceId = event.target.value;
    this.switchWorkspace(selectedWorkspaceId);
    querySelector(".workspace", this).textContent = event.target.nextElementSibling.textContent;
  }

  /**
   * Handles workspace item click events (for editing workspace names).
   * @param {{target: HTMLElement}} event - The click event.
   */
  onWorkspaceClick({ target }) {
    if (target.closest("vt-icon")) {
      const listItem = target.closest("li");
      const editDialog = new UpdateWorkspaceDialog(listItem.textContent);
      const handleUpdate = async ({ detail: newName }) => {
        listItem.firstElementChild.textContent = newName;
        const workspaceId = querySelector("input", listItem).value;
        const { workspaces } = await getStore("workspaces");
        const workspaceIndex = workspaces.findIndex((workspace) => workspace.id === workspaceId);
        workspaces[workspaceIndex].title = newName;
        setStore({ workspaces });
      };
      return addEventListener(editDialog, "update", handleUpdate), this.firstElementChild.appendChild(editDialog);
    }
  }

  /**
   * Renders the workspace management UI.
   * @param {string} currentWorkspaceName - Name of the currently active workspace.
   * @returns {DocumentFragment} The rendered workspace interface.
   */
  render(currentWorkspaceName) {
    const renderWorkspaceItem = (workspace) => templateLiteral`<li>
				<label>
					<input type="radio" name="workspace" value="${workspace.id}" hidden />
					<span>${workspace.name.slice(0, 12)}</span>
				</label>
				<vt-icon ico="edit"></vt-icon>
			</li>`;

    return templateLiteral`<workspaces-popup
				id="workspaces"
				@change=${this.onWorkspaceChange.bind(this)}
				@click=${this.onWorkspaceClick.bind(this)}
				popover>
				${mapArrayToTemplate(this.workspaces, renderWorkspaceItem)}
				<li @click=${this.createWorkspace.bind(this)}>
					<vt-icon ico="plus"></vt-icon><span>Create workspace</span>
				</li>
			</workspaces-popup>
			<div style="padding-top: 0.2em;">
				<span class="workspace">${currentWorkspaceName.slice(0, 10)}</span>
				<button popovertarget="workspaces">
					<vt-icon ico="chev-down" title="switch workspace" toggle></vt-icon>
				</button>
			</div>`;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   * Initializes workspace data and renders the interface.
   */
  async connectedCallback() {
    const {
      workspaceOn: isWorkspaceEnabled,
      workspaces,
      atvWorkspaceId: activeWorkspaceId,
    } = await getStore(["workspaceOn", "workspaces", "atvWorkspaceId"]);

    if (!isWorkspaceEnabled) return;

    this.workspaces = sortWorkspaces(workspaces ?? []);
    if (this.workspaces.length === 0) {
      this.workspaces.push({ id: "workspace", name: "Workspace" });
    }
    this.atvWorkspaceId = activeWorkspaceId || this.workspaces[0].id;

    const currentWorkspaceName = workspaces?.find((workspace) => workspace.id === activeWorkspaceId)?.name ?? i18n("workspace");
    this.replaceChildren(this.render(currentWorkspaceName));
    querySelector(`input[value="${this.atvWorkspaceId}"]`, this).checked = true;

    addEventListener(
      this.firstElementChild,
      "toggle",
      (event) =>
        event.newState === "closed" &&
        (querySelector('vt-icon[ico="chev-down"]', this).checked = false),
    );
  }
}
customElements.define("workspace-tabs", WorkspaceTabsElement);

/**
 * UpdateWorkspaceDialog - Modal dialog for creating or updating workspace names.
 * Provides input field with emoji picker for workspace customization.
 * @element update-workspace-dialog
 * @extends HTMLDialogElement
 */
class UpdateWorkspaceDialog extends HTMLDialogElement {
  /** @type {string|undefined} Existing workspace name for updates */
  workspace;

  /** @type {HTMLElement|undefined} Emoji picker component instance */
  emojiPicker;

  /**
   * @param {string} [existingWorkspaceName] - Name of workspace to update (omit for new workspace)
   */
  constructor(existingWorkspaceName) {
    super();
    this.workspace = existingWorkspaceName;
  }

  /**
   * Handles workspace creation or update by firing appropriate event.
   * Emits either 'create' or 'update' event with the input value.
   */
  updateWorkspace() {
    fireEvent(
      this,
      this.workspace ? "update" : "create",
      querySelector("input", this).value,
    );
    this.remove();
  }

  /**
   * Shows emoji picker for workspace name customization.
   * Lazy loads the emoji picker component on first use.
   * @param {{target: HTMLElement}} event - Click event from emoji button.
   */
  async showEmojiPicker({ target }) {
    if (this.emojiPicker) {
      return this.emojiPicker.showPopover();
    }
    const { EmojiPicker } = await import("./emoji-picker-GN6LRT62.js");
    this.emojiPicker = new EmojiPicker();
    target.before(this.emojiPicker);
  }

  /**
   * Renders the dialog content with form fields and buttons.
   * @returns {DocumentFragment} The rendered dialog content.
   */
  render() {
    return templateLiteral`<h2>${this.workspace ? "Update" : "Create"} ${i18n("workspace")}</h2>
			<label>
				<span>${i18n("name")}</span> <br />
				<input type="text" />
				<span class="emoji-btn" title="Pick emoji" @click=${this.showEmojiPicker.bind(this)}> 😃 </span>
			</label>
			<div>
				<button class="outline-btn" @click=${this.remove.bind(this)}>${i18n("cancel")}</button>
				<button @click=${this.updateWorkspace.bind(this)}>
					${this.workspace ? i18n("update") : i18n("create")}
				</button>
			</div>`;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   * Sets up the dialog and shows it as a modal.
   */
  connectedCallback() {
    this.id = "update-workspace-dialog";
    this.replaceChildren(this.render());
    this.showModal();
    addEventListener(this, "toggle", (event) => event.newState === "closed" && this.remove());
  }
}
customElements.define("update-workspace-dialog", UpdateWorkspaceDialog, { extends: "dialog" });

import windowBarStyles from "./window-bar-QFG34U26.css" with { type: "css" };

/** @type {HTMLElement} Reference to the tab count display element */
let tabCountElement;

/**
 * Updates the tab count display in the window bar.
 * @param {string|number} count - The number of tabs to display.
 */
const updateTabCount = (count) => (tabCountElement.firstChild.data = count);

/**
 * WindowBarElement - Top navigation bar containing tab count, workspace selector, and search.
 * Displays current tab count and provides access to main extension features.
 * @element window-bar
 */
class WindowBarElement extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [windowBarStyles];
  }

  /**
   * Renders the window bar components.
   * @returns {Array<HTMLElement>} Array of child elements to render.
   */
  render() {
    return [
      templateLiteral`<div class="tab-coin">
			<div class="coin-ring">
				<div class="tab-num" ref=${(element) => (tabCountElement = element)}>0</div>
			</div>
		</div>`,
      new WindowSelectorElement(),
      new WorkspaceTabsElement(),
      new SearchTabsElement(),
      new SettingsButtonElement(),
    ];
  }

  /**
   * Lifecycle: called when element is added to DOM.
   * Renders the window bar components.
   */
  async connectedCallback() {
    this.shadowRoot.replaceChildren(...this.render());
  }
}
customElements.define("window-bar", WindowBarElement);
/** @type {HTMLElement} Reference to the tab container element */
let tabContainerElement;

/**
 * Applies theme configuration to the tab container.
 * Updates CSS custom properties for theme, font, and color settings.
 * @param {Object} config - Configuration object with theme settings.
 * @param {string} [config.theme] - Theme name for background image.
 * @param {string} [config.fontFamily] - Font family for text.
 * @param {string} [config.fontSize] - Font size for text.
 * @param {string} [config.textColor] - Text color.
 */
function applyThemeConfiguration(config) {
  tabContainerElement ??= querySelector("tab-container");

  if (config.theme && config.theme !== "none") {
    tabContainerElement.style.setProperty("--tab-bgc", `url(${themeBaseUrl + config.theme + ".svg"})`);
  } else {
    tabContainerElement.style.removeProperty("--tab-bgc");
  }

  if (config.fontFamily) {
    tabContainerElement.style.setProperty("--font-family", config.fontFamily);
  }
  if (config.fontSize) {
    tabContainerElement.style.setProperty("--font-size", config.fontSize);
  }
  if (config.textColor) {
    tabContainerElement.style.setProperty("--txt-clr", config.textColor);
  }
}

// Listen for configuration updates from the extension background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.msg === "config") {
    applyThemeConfiguration(message);
  }
});

/**
 * Automatically suspends tabs that haven't been accessed for a specified time.
 * Helps reduce memory usage by discarding inactive tabs.
 * @param {number} suspensionTimeMs - Time in milliseconds after which to suspend tabs.
 */
async function autoSuspendInactiveTabs(suspensionTimeMs) {
  tabContainerElement ??= querySelector("tab-container");
  const currentTabs = await queryTabs({ windowId });

  /**
   * Suspends a single tab and updates the UI element references.
   * @param {number} tabId - The ID of the tab to suspend.
   */
  function suspendTab(tabId) {
    chrome.tabs
      .discard(tabId)
      .then((discardedTab) => {
        const tabElement = tabContainerElement.shadowRoot.getElementById(String(tabId));
        tabElement.id = discardedTab.id;
        tabElement.tab.id = discardedTab.id;
      })
      .catch((error) => console.error(error));
  }

  currentTabs.forEach((tab) => {
    if (!tab.discarded) {
      if (!tab.lastAccessed) {
        return suspendTab(tab.id);
      }
      if (Date.now() > tab.lastAccessed + suspensionTimeMs) {
        suspendTab(tab.id);
      }
    }
  });
}

// Initialize auto-suspension feature based on user settings
chrome.storage.sync
  .get(["autoSuspensionOn", "autoSuspensionTime"])
  .then((settings) => {
    const { autoSuspensionOn: isEnabled, autoSuspensionTime: timeMinutes } = settings;
    if (!isEnabled) return;

    const suspensionTimeMs = (timeMinutes ?? 30) * 60 * 1000;
    if (isEnabled) {
      setInterval(() => autoSuspendInactiveTabs, 5 * 60 * 60 * 1000, suspensionTimeMs);
    }
  });
import Ot from "./marked-action-BXXJLEWR.css" with { type: "css" };
/** @type {Set<number>} Set of selected tab IDs */
const selectedTabIds = new Set();

/**
 * Handles tab selection checkbox changes.
 * Manages the marked action UI visibility and tab selection state.
 * @param {Event} event - The checkbox change event.
 */
function handleTabSelection(event) {
  const isChecked = event.target.checked;
  const tabId = +event.target.parentElement.id;

  if (isChecked) {
    selectedTabIds.add(tabId);
    if (selectedTabIds.size === 1) {
      document.body.appendChild(new MarkedActionElement());
    } else {
      markedActionInstance.setCount();
    }
  } else {
    selectedTabIds.delete(tabId);
    if (selectedTabIds.size === 0) {
      markedActionInstance.remove();
    } else {
      markedActionInstance.setCount();
    }
  }
}
/**
 * Global reference to the current MarkedActionElement instance.
 * @type {MarkedActionElement|null}
 */
let markedActionInstance = null;

/**
 * Set of marked tab IDs.
 * @type {Set<number>}
 */
let markedTabIds = new Set();

/**
 * Asynchronous function to close tabs by IDs.
 * @param {number[]} tabIds
 * @returns {Promise<void>}
 */
const closeTabsByIds = async (tabIds) => {
  await chrome.tabs.remove(tabIds);
};

/**
 * Asynchronous function to suspend tabs by IDs.
 * @param {number[]} tabIds
 * @returns {Promise<void>}
 */
const suspendTabsByIds = async (tabIds) => {
  await Promise.all(tabIds.map((tabId) => chrome.tabs.discard(tabId)));
};

/**
 * Custom element for marked tab actions.
 * Handles group, window, suspend, and close actions for marked tabs.
 */
class MarkedActionElement extends HTMLElement {
  countElem;
  status = "active";
  marktabs = null;

  constructor() {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [Ot];
    markedActionInstance = this;
  }

  /**
   * Closes all marked tabs.
   * @returns {Promise<void>}
   */
  async closeTabs() {
    try {
      await closeTabsByIds([...markedTabIds]);
      this.clear();
      notify(`${markedTabIds.size} tabs closed`);
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * Suspends all marked tabs.
   * @returns {Promise<void>}
   */
  async suspendTabs() {
    const suspendPromises = [];
    for (const tabId of markedTabIds) {
      suspendPromises.push(chrome.tabs.discard(tabId));
    }
    try {
      await Promise.allSettled(suspendPromises);
      notify(`${markedTabIds.size} tabs suspended`);
    } catch (error) {
      console.error(error);
    }
    this.resetMarked();
  }

  /**
   * Resets all marked tabs.
   */
  resetMarked() {
    fireEvent(document.body, "unselectall");
    this.clear();
  }

  /**
   * Clears all marked tabs and removes the action element.
   */
  clear() {
    markedTabIds.clear();
    this.remove();
    markedActionInstance = null;
  }

  /**
   * Opens the Tab Group selection dialog for marked tabs.
   * @returns {Promise<void>}
   */
  async addInGroup() {
    const { TabgroupSelect } = await import("./tabgroup-select-KETNIX4M.js");
    this.shadowRoot.appendChild(new TabgroupSelect([...markedTabIds]));
  }

  /**
   * Opens the Window selection dialog for marked tabs.
   * @returns {Promise<void>}
   */
  async moveToOtherWindow() {
    const { WindowSelect } = await import("./chunk-EC5AYDH5.js");
    this.shadowRoot.appendChild(new WindowSelect([...markedTabIds]));
  }

  /**
   * Updates the count display for marked tabs.
   */
  setCount() {
    this.countElem.textContent = markedTabIds.size;
  }

  /**
   * Renders the marked action UI.
   * @returns {HTMLElement}
   */
  render() {
    return c`<div class="marked-card">
      <div>
        <output ref=${(elem) => (this.countElem = elem)}>1</output> ${i18n("selected")}
      </div>
      <div class="marked-action-wrapper">
        <vt-icon ico="group" title="${i18n("add_tabs_to_group")}" @click=${this.addInGroup.bind(this)}></vt-icon>
        <vt-icon ico="windows" title="${i18n("move_tabs_to_window")}" @click=${this.moveToOtherWindow.bind(this)}></vt-icon>
        <vt-icon ico="suspend" title="${i18n("suspend_selected_tabs")}" @click=${this.suspendTabs}></vt-icon>
        <vt-icon ico="delete" title="${i18n("close_selected_tabs")}" @click=${this.closeTabs}></vt-icon>
      </div>
      <vt-icon
        ico="close"
        title="${i18n("unselect_all")}"
        style="margin-left: auto;"
        @click=${this.resetMarked.bind(this)}></vt-icon>
    </div>`;
  }

  /**
   * Lifecycle: connected to DOM.
   */
  connectedCallback() {
    this.shadowRoot.replaceChildren(this.render());
  }
}
customElements.define("marked-action", MarkedActionElement);

// ==========================
// Tab Item Component
// ==========================

/**
 * Custom element representing a single tab item.
 */
class TabItemElement extends HTMLElement {
  _internals;
  tab;
  tabIndex = 0;
  id;
  pinned;
  suspend;

  /**
   * @param {object} tabData
   */
  constructor(tabData) {
    super();
    this._internals = this.attachInternals();
    this.tab = tabData;
    this.tabIndex = 0;
    this.id = this.tab.id.toString();
    this.pinned = this.tab.pinned;
    this.suspend = this.tab.discarded;
  }

  set active(isActive) {
    isActive
      ? this._internals.states.add("active")
      : this._internals.states.delete("active");
  }

  set pinned(isPinned) {
    isPinned
      ? this._internals.states.add("pinned")
      : this._internals.states.delete("pinned");
  }

  set collapsed(isCollapsed) {
    isCollapsed
      ? this._internals.states.add("collapsed")
      : this._internals.states.delete("collapsed");
    if (this.nextElementSibling?.tab.groupId === this.tab.groupId) {
      this.nextElementSibling.collapsed = isCollapsed;
    }
  }

  set suspend(isSuspended) {
    isSuspended
      ? this._internals.states.add("suspend")
      : this._internals.states.delete("suspend");
  }

  /**
   * Closes this tab.
   * @param {Event} event
   */
  closeTab(event) {
    event.stopImmediatePropagation();
    chrome.tabs.remove(this.tab.id).catch((error) => console.error(error));
  }

  /**
   * Suspends this tab.
   */
  suspendTab() {
    chrome.tabs
      .discard(this.tab.id)
      .then((tab) => {
        this.id = tab.id.toString();
        this.tab.id = tab.id;
        toast("Tab suspended");
      })
      .catch((error) => console.error(error));
  }

  /**
   * Activates this tab.
   */
  activateTab() {
    chrome.windows
      .update(this.tab.windowId, { focused: true })
      .catch((error) => console.error(error));
    chrome.tabs
      .update(this.tab.id, { active: true })
      .catch((error) => console.error(error));
  }

  /**
   * Renders the tab item UI.
   * @returns {HTMLElement}
   */
  render() {
    const tab = this.tab;
    return c`<input
      type="checkbox"
      class="mark-tab"
      tabindex="-1"
      title="${i18n("select_tab")}"
      @change=${At} />
    <tab-info @click=${this.activateTab.bind(this)}>
      <img src=${() => tab.favIconUrl} loading="lazy" @error=${handleTabIconError} />
      <div>
        <div class="tab-title">${() => tab.title.replaceAll("<", "<")}</div>
        <div class="tab-url">${() => tab.url.slice(8)}</div>
      </div>
    </tab-info>
    <div class="action-btn-box">
      <vt-icon
        ico="sleep"
        class="sleep-btn"
        title="${i18n("suspend_tab")}"
        @click=${this.suspendTab.bind(this)}></vt-icon>
      <vt-icon
        ico="close"
        class="close-btn"
        title="${i18n("close_tab")}"
        @click=${this.closeTab.bind(this)}></vt-icon>
    </div>`;
  }

  /**
   * Lifecycle: connected to DOM.
   */
  connectedCallback() {
    this.setAttribute("draggable", "true");
    this.replaceChildren(this.render());
  }
}
customElements.define("tab-item", TabItemElement);

/**
 * Handles tab icon loading error.
 * @this {HTMLImageElement}
 */
function handleTabIconError() {
  this.src = "/assets/web.svg";
}

// ==========================
// Tab Action Menu Component
// ==========================

let tabActionMenuInstance = null;

/**
 * Custom element for tab action menu (context menu).
 */
class TabActionMenuElement extends HTMLElement {
  tabId;
  tabIndex;
  windowId;

  constructor() {
    super();
    tabActionMenuInstance = this;
  }

  /**
   * Sets the tab context for the menu.
   * @param {number} tabId
   * @param {number} tabIndex
   * @param {number} windowId
   */
  setTab(tabId, tabIndex, windowId) {
    this.tabId = tabId;
    this.tabIndex = tabIndex;
    this.windowId = windowId;
    setTimeout(() => this.showPopover(), 100);
  }

  menuActions = {
    add_previous_tab: () => chrome.tabs.create({ index: this.tabIndex }),
    add_next_tab: () => chrome.tabs.create({ index: this.tabIndex + 1 }),
    add_tab_to_group: () => F(this.tabId),
    move_tab_to_window: () => z(this.tabId),
    reload: () => chrome.tabs.reload(this.tabId),
    duplicate: () => chrome.tabs.duplicate(this.tabId),
    pin: () => chrome.tabs.update({ pinned: true }).catch((error) => console.error(error)),
    mute: () => chrome.tabs.update({ muted: true }).catch((error) => console.error(error)),
    suspend: () => chrome.tabs.discard(this.tabId).catch((error) => console.error(error)),
    copy_url: async () => {
      const { url } = await P(this.tabId);
      navigator.clipboard
        .writeText(url)
        .then(() => toast("Copied"))
        .catch((error) => console.error(error));
    },
    close_above_tabs: async () => {
      const tabs = (await r({ windowId: this.windowId }))
        .slice(0, this.tabIndex)
        .map((tab) => tab.id);
      chrome.tabs.remove(tabs).catch((error) => console.error(error));
    },
    close_below_tabs: async () => {
      const tabs = (await r({ windowId: this.windowId }))
        .slice(this.tabIndex)
        .map((tab) => tab.id);
      chrome.tabs.remove(tabs).catch((error) => console.error(error));
    },
    close_other_tabs: j,
  };

  /**
   * Handles menu item click.
   * @param {Event} event
   */
  async onMenuItemClick({ target }) {
    const menuId = target.closest("li")?.id;
    if (menuId) {
      await this.menuActions[menuId]();
      this.hidePopover();
    }
  }

  /**
   * Renders the tab action menu UI.
   * @returns {HTMLElement[]}
   */
  render() {
    const renderMenuItem = (item) => c`<li id=${item.id}>
      <vt-icon ico="${item.icon}" title="edit group"></vt-icon>
      <span>${item.title}</span>
      <kbd>${item.keyShortcut}</kbd>
    </li>`;
    return [
      {
        id: "add_previous_tab",
        icon: "plus",
        title: i18n("new_tab_above"),
        keyShortcut: "Ctrl+Alt+N",
      },
      {
        id: "add_next_tab",
        icon: "tab-plus",
        title: i18n("new_tab_below"),
        keyShortcut: "Alt+N",
      },
      {
        id: "add_tab_to_group",
        icon: "group",
        title: i18n("add_tab_to_group"),
        keyShortcut: "Alt+G",
      },
      {
        id: "move_tab_to_window",
        icon: "move",
        title: i18n("move_tab_to_window"),
        keyShortcut: "Alt+M",
      },
      {
        id: "reload",
        icon: "reload",
        title: i18n("reload_tab"),
        keyShortcut: "Ctrl+R",
      },
      {
        id: "duplicate",
        icon: "duplicate",
        title: i18n("duplicate_tab"),
        keyShortcut: "Alt+D",
      },
      { id: "pin", icon: "pin", title: i18n("pin_tab"), keyShortcut: "" },
      { id: "mute", icon: "mute", title: i18n("mute_tab"), keyShortcut: "" },
      {
        id: "suspend",
        icon: "sleep",
        title: i18n("suspend_tab"),
        keyShortcut: "Alt+H",
      },
      {
        id: "copy_url",
        icon: "copy",
        title: i18n("copy_url"),
        keyShortcut: "Ctrl+C",
      },
      {
        id: "close_above_tabs",
        icon: "close-multiple",
        title: i18n("close_above_tabs"),
        keyShortcut: "Ctrl+Shift+Q",
      },
      {
        id: "close_below_tabs",
        icon: "close-multiple",
        title: i18n("close_below_tabs"),
        keyShortcut: "Ctrl+Alt+W",
      },
      {
        id: "close_other_tabs",
        icon: "close-multiple",
        title: i18n("close_other_tabs"),
        keyShortcut: "Ctrl+Alt+Q",
      },
    ].map(renderMenuItem);
  }

  /**
   * Lifecycle: connected to DOM.
   */
  connectedCallback() {
    this.replaceChildren(...this.render());
    this.setAttribute("popover", "");
    $on(this, "click", this.onMenuItemClick);
  }
}
customElements.define("tabaction-menu", TabActionMenuElement);

// ==========================
// (Continue with GroupBarElement, TabContainerElement, etc.)
// ==========================

// ==========================
// Group Bar Component
// ==========================

/**
 * Custom element representing a tab group bar.
 */
class GroupBarElement extends HTMLElement {
  tabGroup;
  _internals;
  /**
   * @param {object} tabGroup
   */
  constructor(tabGroup) {
    super();
    this.tabGroup = tabGroup;
    this._internals = this.attachInternals();
    this.collapsed = this.tabGroup.collapsed;
  }

  set title(newTitle) {
    if (this.tabGroup.title !== newTitle) {
      this.tabGroup.title = newTitle;
      this.children[1].textContent = newTitle;
    }
  }

  set color(newColor) {
    if (this.tabGroup.color !== newColor) {
      this.tabGroup.color = newColor;
      this.updateGroupTabsColor(newColor);
    }
  }

  get collapsed() {
    return this._internals.states.has("collapsed");
  }

  set collapsed(isCollapsed) {
    if (isCollapsed) {
      this._internals.states.add("collapsed");
    } else {
      this._internals.states.delete("collapsed");
    }
    if (this.tabGroup.collapsed !== isCollapsed) {
      this.tabGroup.collapsed = isCollapsed;
      this.parentElement.collapsed = this.collapsed;
    }
  }

  /**
   * Toggles the collapsed state of the group.
   */
  toggleGroup() {
    this.collapsed = !this.tabGroup.collapsed;
    this.tabGroup.collapsed = this.collapsed;
    chrome.tabGroups.update(this.tabGroup.id, { collapsed: this.collapsed });
  }

  /**
   * Updates group info from event detail.
   * @param {{detail: {title: string, color: string}}} param0
   */
  updateGroup({ detail }) {
    this.title = detail.title;
    this.color = detail.color;
    chrome.tabGroups.update(this.tabGroup.id, {
      title: detail.title,
      color: detail.color,
    });
  }

  /**
   * Ungroups all tabs in this group.
   */
  async unGroupTab() {
    const tabIds = (await r({ groupId: this.tabGroup.id })).map(({ id }) => id);
    await chrome.tabs.ungroup(tabIds).catch((error) => console.error(error));
  }

  /**
   * Removes all tabs in this group.
   */
  async removeTabs() {
    const tabIds = (await r({ groupId: this.tabGroup.id })).map(({ id }) => id);
    await chrome.tabs.remove(tabIds).catch((error) => console.error(error));
  }

  /**
   * Suspends all tabs in this group.
   */
  async suspendGroup() {
    try {
      const suspendPromises = [];
      const tabs = await r({ groupId: this.tabGroup.id });
      for (const tab of tabs) suspendPromises.push(chrome.tabs.discard(tab.id));
      await Promise.all(suspendPromises);
      notify("Tabgroup suspended");
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * Moves the tab group to another window.
   */
  moveTabGroup() {
    this.appendChild(new MoveTabGroupDialog(null, this.tabGroup.id));
  }

  /**
   * Opens the edit dialog for the tab group.
   */
  editTabGroup() {
    this.appendChild(new EditTabGroupDialog(this.tabGroup));
  }

  /**
   * Renders the group bar UI.
   * @returns {HTMLElement}
   */
  render() {
    return c`<vt-icon
      ico="chev-down"
      class="collapse-btn"
      title="${i18n("collapse/uncollapse_group")}"
      @click=${this.toggleGroup.bind(this)}></vt-icon>
    <span>${this.tabGroup.title}</span>
    <div class="group-action">
      <vt-icon ico="edit" title="${i18n("edit_group")}" @click=${this.editTabGroup.bind(this)}></vt-icon>
      <vt-icon ico="windows" title="${i18n("move_group_to_window")}" @click=${this.moveTabGroup.bind(this)}></vt-icon>
      <vt-icon ico="suspend" title="${i18n("suspend_tabgroup")}" @click=${this.suspendGroup.bind(this)}></vt-icon>
      <vt-icon ico="ungroup" title="${i18n("ungroup_tabs")}" @click=${this.unGroupTab.bind(this)}></vt-icon>
      <vt-icon ico="delete" title="${i18n("delete_group")}" @click=${this.removeTabs.bind(this)}></vt-icon>
    </div>`;
  }

  /**
   * Lifecycle: connected to DOM.
   */
  connectedCallback() {
    this.setAttribute("tabindex", "0");
    this.replaceChildren(this.render());
    b(this.parentElement, this.tabGroup.color);
    setTimeout($t, 100, this.tabGroup);
  }

  /**
   * Updates the color of all tabs in this group.
   * @param {string} color
   */
  updateGroupTabsColor(color) {
    b(this.parentElement, color);
    let sibling = this.parentElement.nextElementSibling;
    while (sibling?.tab.groupId === this.tabGroup.id) {
      b(sibling, color);
      sibling = sibling.nextElementSibling;
    }
  }
}
customElements.define("group-bar", GroupBarElement);

// ==========================
// Tab Container Component
// ==========================

/**
 * Custom element for the tab container.
 */
class TabContainerElement extends HTMLElement {
  groupMap = new Map();
  activeTab;
  windowId;
  tabs = [];

  constructor() {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [Nt, Ft];
    f("theme").then((theme) => nt(theme));
  }

  /**
   * Renders all tab items.
   * @param {Map<number, string>} groupColors
   * @returns {HTMLElement[]}
   */
  render(groupColors) {
    return Z(this.tabs, (tabData) => {
      const tabItem = new TabItemElement(tabData);
      if (tabData.groupId !== -1) {
        if (!this.groupMap.has(tabData.groupId)) {
          this.groupMap.set(tabData.groupId, tabItem);
        }
        b(tabItem, groupColors.get(tabData.groupId));
      }
      return tabItem;
    });
  }

  /**
   * Lifecycle: connected to DOM.
   */
  async connectedCallback() {
    const { compactMode } = await f("compactMode");
    if (compactMode) this.setAttribute("compact", "");
    this.windowId = (await chrome.windows.getCurrent()).id;
    await this.setWindowTabs(this.windowId);
    this.setListener();
    $on(this.shadowRoot, "contextmenu", at);
    $on(this.shadowRoot, "dragstart", ({ target }) => {
      rt = target.closest("tab-item").id;
    });
    $on(this.shadowRoot, "dragover", (e) => e.preventDefault());
    $on(this.shadowRoot, "drop", this.setDropped.bind(this));
    $on(this.shadowRoot, "auxclick", (e) => {
      if (e.which === 2) u(+e.target.closest("tab-item").id);
    });
    $on(document.body, "windowswitch", ({ detail }) => this.setWindowTabs(detail));
    $on(document.body, "unselectall", () => {
      for (const child of this.shadowRoot.children) {
        child.firstElementChild.checked = false;
      }
    });
    addEventListener("paste", Ct);
  }

  /**
   * Loads and displays all tabs for a window.
   * @param {number} windowId
   */
  async setWindowTabs(windowId) {
    this.windowId = windowId;
    this.groupMap.clear();
    try {
      const tabs = await r({ windowId });
      const groups = await D({ windowId });
      const groupColors = new Map(groups.map((g) => [g.id, g.color]));
      this.tabs = W(tabs);
      this.shadowRoot.replaceChildren(this.render(groupColors));
      if (this.groupMap.size === 0 || groups.length > 0) {
        groups.forEach(this.insertTabGroup, this);
      }
      r({ active: true, windowId }).then((activeTabs) => this.activateTab(activeTabs[0].id));
      G(tabs.length);
    } catch (error) {
      console.error(error);
      document.body.appendChild(new K(error));
    }
  }

  /**
   * Inserts a group bar for a tab group.
   * @param {object} group
   */
  insertTabGroup(group) {
    if (!this.groupMap.has(group.id)) return;
    const groupBar = new GroupBarElement(group);
    this.groupMap.get(group.id).prepend(groupBar);
    this.groupMap.set(group.id, groupBar);
    $on(groupBar, "remove", ({ detail }) => this.onRemoveGroup(detail));
  }

  /**
   * Handles tab drop (drag-and-drop).
   * @param {{target: HTMLElement}} param0
   */
  async setDropped({ target }) {
    const targetIndex = target.closest("tab-item").tab.index;
    await chrome.tabs
      .move(+rt, { index: targetIndex })
      .catch(() => notify("cannot move tab", "error"))
      .finally(() => (rt = null));
  }
}
customElements.define("tab-container", TabContainerElement);
