/**
 * Chrome Extension Utilities and Reactive DOM Helpers
 * Provides bound Chrome API methods, color constants, dynamic DOM fragment utilities,
 * and a lightweight reactive data binding system for DOM manipulation.
 *
 * @module ChromeExtensionUtils
 */

// --- Chrome API Bindings ---

/** Query all tabs. */
const queryTabs = chrome.tabs.query.bind(chrome.tabs);
/** Get a tab by ID. */
const getTab = chrome.tabs.get.bind(chrome.tabs);
/** Create a new tab. */
const createTab = chrome.tabs.create.bind(chrome.tabs);
/** Remove a tab by ID. */
const removeTab = chrome.tabs.remove.bind(chrome.tabs);
/** Query all tab groups. */
const queryTabGroups = chrome.tabGroups.query.bind(chrome.tabGroups);
/** Move tab groups. */
const moveTabGroups = chrome.tabGroups.move.bind(chrome.tabGroups);
/** Get item from localStorage. */
const getLocalStorageItem = localStorage.getItem.bind(localStorage);
/** Set item in localStorage. */
const setLocalStorageItem = localStorage.setItem.bind(localStorage);
/** Get item from chrome.storage.sync. */
const getSyncStorageItem = chrome.storage.sync.get.bind(chrome.storage.sync);
/** Set item in chrome.storage.sync. */
const setSyncStorageItem = chrome.storage.sync.set.bind(chrome.storage.sync);
/** Get item from chrome.storage.session. */
const getSessionStorageItem = chrome.storage.session.get.bind(chrome.storage.session);
/** Set item in chrome.storage.session. */
const setSessionStorageItem = chrome.storage.session.set.bind(chrome.storage.session);
/** Get item from sessionStorage. */
const getSessionItem = sessionStorage.getItem.bind(sessionStorage);
/** Set item in sessionStorage. */
const setSessionItem = sessionStorage.setItem.bind(sessionStorage);

// --- Color Constants ---

/** List of supported tab group colors. */
const TAB_GROUP_COLORS = [
  "blue", "red", "yellow", "green", "cyan", "purple", "yellow", "pink", "orange", "grey"
];

/** Mapping of color names to RGB values. */
const COLOR_RGB_MAP = {
  blue: "0, 0, 255",
  red: "255, 0, 0",
  yellow: "255, 251, 0",
  green: "0, 128, 0",
  cyan: "0, 255, 255",
  purple: "128, 0, 128",
  pink: "255, 192, 203",
  orange: "255, 165, 0",
  grey: "128, 128, 128",
};

/** Base URL for extension themes. */
const THEME_BASE_URL = "https://crxextstatic.blob.core.windows.net/themes/";

// --- Reactive DOM Binding System ---

/**
 * Internal state for DOM binding operations.
 * @private
 */
const domBindingState = {
  node: null,
  attr: null,
  valFn: null,
  fragFn: null,
  style: null,
  cmt: null,
};

/** Set of attributes that require special handling for value binding. */
const VALUE_ATTRIBUTES = new Set(["value", "checked", "open"]);

/**
 * Represents a reactive DOM node binding.
 * @private
 */
class ReactiveNodeBinding {
  constructor() {
    this.node = domBindingState.node;
    this.attr = domBindingState.attr;
    this.valFn = domBindingState.valFn;
    this.style = domBindingState.style;
  }
}

/**
 * Represents a reactive comment node binding (for fragments).
 * @private
 */
class ReactiveCommentBinding {
  constructor() {
    this.fragFn = domBindingState.fragFn;
    this.cmtNode = domBindingState.cmt;
  }
}

/**
 * Reactive update helpers for DOM nodes and fragments.
 * @private
 */
const reactiveUpdateHelpers = {
  /**
   * Replace a comment node with a fragment or node(s).
   * @param {Comment} commentNode
   * @param {Node|Node[]} replacement
   */
  replaceChildFragment(commentNode, replacement) {
    if (Array.isArray(replacement)) {
      commentNode.parentNode.replaceChildren(commentNode, ...replacement);
    } else {
      commentNode.parentNode.replaceChild(replacement, commentNode.nextSibling);
    }
  },

  /**
   * Set a sibling node for a comment node using a fragment function.
   * @param {Object} binding
   */
  setSibling({ cmtNode, fragFn }) {
    const fragment = fragFn();
    if (fragment instanceof Promise) {
      fragment.then(resolved => reactiveUpdateHelpers.replaceChildFragment(cmtNode, resolved));
    } else {
      reactiveUpdateHelpers.replaceChildFragment(
        cmtNode,
        fragment || new Comment(String(Math.random()).slice(9))
      );
    }
  },

  /**
   * Set the value of a bound node.
   * @param {Object} binding
   */
  setValue(binding) {
    const node = binding.node;
    if (binding.attr) {
      if (binding.style) {
        node.style.setProperty(binding.style, binding.valFn());
      } else if (node[binding.attr] !== undefined && node instanceof HTMLElement) {
        node[binding.attr] = binding.valFn();
      } else {
        node.setAttribute(binding.attr, binding.valFn());
      }
    } else {
      node.textContent = binding.valFn();
    }
  },

  /**
   * Update a binding (either a comment fragment or a value).
   * @param {Object} binding
   */
  update(binding) {
    if (binding.cmtNode) {
      reactiveUpdateHelpers.setSibling(binding);
    } else {
      reactiveUpdateHelpers.setValue(binding);
    }
  },
};

/**
 * Reactivity helpers for DOM attribute binding and data storage.
 * @private
 */
const domReactivityHelpers = {
  /**
   * Set up two-way binding for input elements.
   * @param {HTMLElement} element
   * @param {string} attr
   * @param {Object} dataObj
   * @param {string} key
   */
  setBinding(element, attr, dataObj, key) {
    const valueKey = element.valueAsNumber ? "valueAsNumber" : attr;
    element.addEventListener(element.bindEvent, function () {
      dataObj[key] = this[valueKey];
    });
  },

  /**
   * Insert a reactive data binding into a map.
   * @param {Object} obj
   * @param {string} key
   * @param {any} binding
   */
  insertReactiveData(obj, key, binding) {
    if (obj._reactMap.has(key)) {
      const existing = obj._reactMap.get(key);
      Array.isArray(existing)
        ? existing.push(binding)
        : obj._reactMap.set(key, [existing, binding]);
    } else {
      obj._reactMap.set(key, binding);
    }
  },

  /**
   * Create a new reactive data binding.
   * @param {Object} obj
   * @param {string} key
   * @returns {ReactiveNodeBinding|ReactiveCommentBinding}
   */
  makeReactiveData(obj, key) {
    let binding;
    if (domBindingState.node) {
      binding = new ReactiveNodeBinding();
      if (
        VALUE_ATTRIBUTES.has(domBindingState.attr) &&
        domBindingState.node.bindEvent
      ) {
        domReactivityHelpers.setBinding(domBindingState.node, domBindingState.attr, ...arguments);
      }
    } else if (domBindingState.cmt) {
      binding = new ReactiveCommentBinding();
    }
    return binding;
  },
};

/**
 * Recursively wraps an object or array in a Proxy to enable reactivity.
 * @param {Object|Array} obj
 * @returns {Proxy}
 */
function makeReactive(obj) {
  if (obj._reactMap || typeof obj !== "object") return obj;
  if (Array.isArray(obj)) return makeReactiveArray(obj);

  Object.defineProperty(obj, "_reactMap", { value: new Map(), enumerable: false });
  for (const key in obj) {
    if (obj[key] && typeof obj[key] === "object") {
      obj[key] = Array.isArray(obj[key])
        ? makeReactiveArray(obj[key])
        : makeReactive(obj[key]);
    }
  }
  return new Proxy(obj, {
    get(target, prop, receiver) {
      const binding = domReactivityHelpers.makeReactiveData(target, prop);
      if (binding) domReactivityHelpers.insertReactiveData(target, prop, binding);
      return Reflect.get(target, prop, receiver);
    },
    set(target, prop, value) {
      if (typeof value !== "object" && target[prop] === value) return true;
      Reflect.set(target, prop, value);
      const binding = target._reactMap.get(prop);
      if (binding) {
        Array.isArray(binding)
          ? binding.forEach(reactiveUpdateHelpers.update)
          : reactiveUpdateHelpers.update(binding);
      }
      return true;
    },
    deleteProperty(target, prop) {
      const binding = target._reactMap.get(prop);
      if (Array.isArray(binding)) {
        binding.forEach((b, idx) => {
          if (!b.node.isConnected) binding.splice(idx, 1);
        });
      } else {
        target._reactMap.delete(prop);
      }
      delete target[prop];
      return true;
    },
  });
}

/**
 * Wraps an array in a Proxy to enable reactivity for array operations.
 * @param {Array} arr
 * @returns {Proxy}
 */
function makeReactiveArray(arr) {
  if (!Array.isArray(arr)) {
    console.error("input object must be array");
    return;
  }
  arr = arr.map(item => makeReactive(item));
  let nodeBinding;
  const wrapArray = (items) => items.map(item => makeReactive(item));
  const mapArray = (items, fn) => items.map(fn);

  function insertAfter(index, items, context) {
    const nodes = mapArray(items, context.yieldfn);
    if (!nodes) return;
    const isArray = Array.isArray(nodes);
    if (index === 0) {
      isArray ? context.comment.after(...nodes) : context.comment.after(nodes);
      return;
    }
    const prev = context.comment.parentNode.children[index - 1];
    isArray ? prev.after(...nodes) : prev.after(nodes);
  }

  function updateItem(index, item, context) {
    if (context.updateItem) {
      const node = context.comment.parentNode.children[index];
      if (node) context.updateItem(node, item);
    } else {
      context.comment.parentNode.children[index].replaceWith(context.yieldfn(item));
    }
  }

  function removeItems(index, count, context) {
    if (!(count <= 0)) {
      for (let i = 0; i < count; i++) {
        context.comment.parentNode.children[index]?.remove();
      }
    }
  }

  function updateFragment(index, count, items) {
    const context = arr._react[0];
    let mapped;
    if (items) {
      if (count > 0) {
        mapped ??= items.splice(0, count);
        const len = mapped.length;
        if (len !== 0) {
          for (let i = 0; i < len; i++) updateItem(index + i, mapped[i], context);
        }
        removeItems(index + len, count - len, context);
      }
      if (items) insertAfter(index, items, context);
    } else if (count > 0) {
      removeItems(index, count, context);
    }
  }

  const arrayMethods = {
    push: () =>
      function (...items) {
        items = wrapArray(items);
        arr.push.call(arr, ...items);
        if (arr._react) {
          for (const context of arr._react) {
            const nodes = mapArray(items, context.yieldfn);
            const parent = context.comment.parentElement;
            Array.isArray(nodes) ? parent.append(...nodes) : parent.append(nodes);
          }
          nodeBinding && reactiveUpdateHelpers.update(nodeBinding);
        }
      },
    pop: () =>
      function () {
        arr.pop.apply(arr, arguments);
        arr._react[0].comment.parentNode.lastElementChild.remove();
      },
    splice: () =>
      function (start, deleteCount, ...items) {
        if (!arr._react) return arr.splice.apply(arr, arguments);
        items = wrapArray(items);
        const removed = arr.splice.call(arr, start, deleteCount, ...items);
        updateFragment(start, deleteCount, items);
        nodeBinding && items.length - deleteCount !== 0 && reactiveUpdateHelpers.update(nodeBinding);
        return removed;
      },
    shift: () =>
      function () {
        arr.shift.apply(arr, arguments);
        updateFragment(0, 1);
      },
    unshift: () =>
      function (...items) {
        arr.unshift.apply(arr, arguments);
        updateFragment(0, 0, items);
      },
    filter: () =>
      function () {
        if (!arr._react) return arr.filter.apply(arr, arguments);
        const predicate = arguments[0];
        const context = arr._react[0];
        const children = context.comment.parentNode.children;
        let len = arr.length;
        while (len--) {
          if (!predicate(arr[len])) {
            children[len]?.remove();
            arr.splice(len, 1);
          }
        }
        return this;
      },
    reverse: () =>
      function () {
        arr.reverse.apply(arr, arguments);
        const context = arr._react[0];
        context.comment.parentNode.append(
          ...Array.from(context.comment.parentNode.childNodes).reverse()
        );
      },
    sort: () =>
      function () {
        arr.sort.apply(arr, arguments);
        updateFragment(0, arr.length, [...arr]);
      },
    length: () => (
      domBindingState.cmt
        ? (nodeBinding = new ReactiveCommentBinding())
        : domBindingState.node && (nodeBinding = new ReactiveNodeBinding()),
      arr.length
    ),
  };

  return new Proxy(arr, {
    get(target, prop, receiver) {
      return arrayMethods[prop] ? arrayMethods[prop]() : Reflect.get(target, prop, receiver);
    },
    deleteProperty(target, prop) {
      arr._react[0].comment.parentNode.children[Number(prop)].remove();
      delete target[prop];
      return true;
    },
    defineProperty(target, prop, descriptor) {
      Object.defineProperty(target, prop, descriptor);
      return Reflect.get(target, prop);
    },
  });
}

// --- Template Literal Parsing and Fragment Utilities ---

const ATTRIBUTE_OBJECT_REGEX = /\s\.(.*)=$/;
const commentFragmentMap = new Map();
const attributeFunctionMap = new Map();
const functionMap = new Map();
const objectMap = new Map();

/**
 * Template literal extraction and dynamic fragment handling.
 * @private
 */
const templateExtractor = {
  strings: null,
  stringArr: null,

  /**
   * Extracts and processes template literal arguments.
   * @param {TemplateStringsArray} strings
   * @param  {...any} values
   * @returns {string}
   */
  extract(strings, ...values) {
    this.strings = strings;
    this.stringArr = [strings[0]];
    const valueCount = values.length;
    for (let i = 0; i < valueCount; i++) {
      const value = values[i];
      const typeName = value?.constructor?.name;
      if (this.instances[typeName]) {
        this.instances[typeName](value, i);
      } else if (typeof value === "object") {
        this.setObjectHolder(value, i);
      } else {
        this.stringArr.push(value, strings[i + 1]);
      }
    }
    const result = "".concat(...this.stringArr);
    this.strings = null;
    this.stringArr = null;
    return result;
  },

  instances: {
    Function: (value, idx) =>
      value.name
        ? templateExtractor.extractFunction(value, idx)
        : templateExtractor.extractReactFunction(value, idx),
    AsyncFunction: (value, idx) => templateExtractor.extractFunction(value, idx),
    DocumentFragment: (value, idx) => commentFragmentMap.set(templateExtractor.setComment(idx), value),
    Array: (value, idx) => templateExtractor.extractArray(value, idx),
    Promise: (value, idx) => templateExtractor.extractPromise(value, idx),
    Object: (value, idx) => templateExtractor.setObjectHolder(value, idx),
  },

  extractFunction(fn, idx) {
    const key = String(Math.random()).slice(9);
    functionMap.set(key, fn);
    this.stringArr.push(key, this.strings[idx + 1]);
  },

  extractReactFunction(fn, idx) {
    if (this.strings[idx].endsWith("=") || this.strings[idx].endsWith(":")) {
      this.setReactHolder(fn, idx);
    } else if (this.strings[idx].endsWith(">")) {
      commentFragmentMap.set(this.setComment(idx), fn);
    } else if (this.strings[idx].trim().endsWith(">")) {
      commentFragmentMap.set(this.setComment(idx, "?^"), fn);
    } else {
      commentFragmentMap.set(this.setComment(idx, this.strings[idx] || "?^"), fn);
    }
  },

  extractArray(arr, idx) {
    if (this.strings[idx].endsWith("=")) {
      this.setObjectHolder(arr, idx);
    } else if (arr[0] instanceof Node) {
      commentFragmentMap.set(this.setComment(idx), arr);
    } else {
      this.stringArr.push(...arr, this.strings[idx + 1]);
    }
  },

  setObjectHolder(obj, idx) {
    const match = this.strings[idx].match(ATTRIBUTE_OBJECT_REGEX)?.[1];
    if (!match) {
      console.warn("object only pass at attribute");
      return;
    }
    objectMap.set(match, obj);
    this.stringArr.push(match, this.strings[idx + 1]);
  },

  setComment(idx, prefix = "?") {
    const key = ` ${prefix}${String(Math.random()).slice(9)} `;
    this.stringArr.push(`<!--${key}--> `, this.strings[idx + 1]);
    return key;
  },

  extractPromise(promise, idx) {
    const key = this.setComment(idx, "");
    promise.then(result => fragmentUtils.setPromiseFragment(key, result));
  },

  setReactHolder(fn, idx) {
    const key = "%" + String(Math.random()).slice(9);
    attributeFunctionMap.set(key, fn);
    this.stringArr.push(key, this.strings[idx + 1]);
  },
};

/**
 * Utilities for handling dynamic DOM fragments and promises.
 * @private
 */
const fragmentUtils = {
  /**
   * Replace a comment node with a fragment or node(s).
   * @param {Comment} commentNode
   * @param {Node|Node[]} replacement
   */
  replaceChildFragment(commentNode, replacement) {
    if (Array.isArray(replacement)) {
      commentNode.parentNode.replaceChildren(commentNode, ...replacement);
    } else {
      commentNode.parentNode.replaceChild(replacement, commentNode.nextSibling);
    }
  },

  /**
   * Insert a fragment or text node after a comment node.
   * @param {Comment} commentNode
   * @param {Function} fragFn
   */
  insertReactChildFragment(commentNode, fragFn) {
    domBindingState.cmt = commentNode;
    domBindingState.fragFn = fragFn;
    const fragment = domBindingState.fragFn();
    if (fragment instanceof Promise) {
      fragment.then(resolved => this.replaceChildFragment(commentNode, resolved));
    } else {
      commentNode.after(fragment || new Comment(String(Math.random()).slice(9)));
    }
    domBindingState.fragFn = domBindingState.cmt = null;
  },

  /**
   * Insert a text node after a comment node.
   * @param {Comment} commentNode
   * @param {Function} textFn
   */
  insertReactTextContent(commentNode, textFn) {
    domBindingState.node = new Text();
    domBindingState.valFn = textFn;
    domBindingState.node.textContent = domBindingState.valFn();
    commentNode.parentNode.replaceChild(domBindingState.node, commentNode);
    domBindingState.node = domBindingState.valFn = null;
  },

  /**
   * Replace comment nodes in a fragment with their corresponding content.
   * @param {DocumentFragment} fragment
   */
  setChildFragment(fragment) {
    const iterator = document.createNodeIterator(
      fragment,
      NodeFilter.SHOW_COMMENT,
      node => node.nodeValue?.startsWith(" ?")
        ? NodeFilter.FILTER_ACCEPT
        : NodeFilter.FILTER_REJECT
    );
    let commentNode;
    while ((commentNode = iterator.nextNode())) {
      const value = commentFragmentMap.get(commentNode.nodeValue);
      if (value) {
        if (typeof value === "function") {
          if (commentNode.nodeValue.startsWith(" ?^")) {
            this.insertReactChildFragment(commentNode, value);
          } else {
            this.insertReactTextContent(commentNode, value);
          }
        } else if (Array.isArray(value)) {
          commentNode.after(...value);
        } else {
          commentNode.parentNode.replaceChild(value, commentNode);
        }
        commentFragmentMap.delete(commentNode.nodeValue);
      }
    }
  },

  /**
   * Replace a comment node (by key) with a resolved promise fragment.
   * @param {string} key
   * @param {Node|Node[]} fragment
   */
  setPromiseFragment(key, fragment) {
    const iterator = document.createNodeIterator(
      document.body,
      NodeFilter.SHOW_COMMENT,
      node => node.nodeValue === key
        ? NodeFilter.FILTER_ACCEPT
        : NodeFilter.FILTER_REJECT
    );
    const commentNode = iterator.nextNode();
    if (commentNode) this.replaceChildFragment(commentNode, fragment);
  },
};

// --- Attribute Parsing and Binding ---

/**
 * Attribute parsing and binding handlers for custom DOM reactivity.
 * @private
 */
const attributeBindingHandlers = {
  mutatingNodes: {
    DETAILS: "toggle",
    DIALOG: "close",
    INPUT: "change",
    SELECT: "change",
    TEXTAREA: "change",
  },

  /**
   * Event binding handler for attributes starting with '@'.
   */
  "@": (element, attr, attributes) => {
    element.addEventListener(attr.name.slice(1), functionMap.get(attr.value));
    attributes.removeNamedItem(attr.name);
    return true;
  },

  /**
   * Retrieve and bind attribute value from function map.
   */
  rtvAttr(element, attr, attributes) {
    domBindingState.node = element;
    domBindingState.attr = attr.name.slice(1);
    domBindingState.valFn = attributeFunctionMap.get(attr.value);
    if (!domBindingState.valFn) {
      return console.error(`${attr.name} value is not a function or not found`);
    }
    element[domBindingState.attr] = domBindingState.valFn();
    domBindingState.node = domBindingState.attr = domBindingState.valFn = null;
  },

  /**
   * Handler for attributes starting with '.' (property binding).
   */
  ".": (element, attr, attributes) => {
    if (attributeBindingHandlers.mutatingNodes[element.tagName]) {
      element.bindEvent = attributeBindingHandlers.mutatingNodes[element.tagName];
    }
    if (attributeFunctionMap.has(attr.value)) {
      attributeBindingHandlers.rtvAttr(element, attr, attributes);
    } else {
      element[attr.name.slice(1)] = objectMap.get(attr.value);
    }
    attributes.removeNamedItem(attr.name);
    return true;
  },

  /**
   * Handler for attributes starting with '?' (boolean binding).
   */
  "?": (element, attr, attributes) => {
    if (attributeBindingHandlers.mutatingNodes[element.tagName]) {
      element.bindEvent = attributeBindingHandlers.mutatingNodes[element.tagName];
    }
    if (attributeFunctionMap.has(attr.value)) {
      attributeBindingHandlers.rtvAttr(element, attr, attributes);
    } else {
      element[attr.name.slice(1)] = attr.value === "true";
    }
    attributes.removeNamedItem(attr.name);
    return true;
  },

  /**
   * Handler for 'ref' attribute (reference callback).
   */
  r: (element, attr, attributes) => {
    if (attr.name === "ref") {
      attributeFunctionMap.get(attr.value)?.(element);
      attributes.removeNamedItem(attr.name);
      return true;
    }
  },

  /**
   * Handler for 'style' attribute with dynamic values.
   */
  s: (element, attr) => {
    if (attr.name !== "style") return;
    domBindingState.node = element;
    domBindingState.attr = attr.name;
    const stylePairs = attr.value.split(";");
    for (const pair of stylePairs) {
      const [styleName, styleValue] = pair.split(":", 2);
      if (styleValue?.startsWith("%")) {
        domBindingState.style = styleName;
        domBindingState.valFn = attributeFunctionMap.get(styleValue);
        element.style.setProperty(styleName, domBindingState.valFn());
      }
    }
    domBindingState.node = domBindingState.attr = domBindingState.style = domBindingState.valFn = null;
    return true;
  },

  /**
   * Handler for attributes starting with '%' (dynamic attribute value).
   */
  "%": (element, attr) => {
    domBindingState.node = element;
    domBindingState.attr = attr.name;
    domBindingState.valFn = attributeFunctionMap.get(attr.value);
    element.setAttribute(attr.name, domBindingState.valFn());
    domBindingState.node = domBindingState.attr = domBindingState.valFn = null;
  },

  /**
   * Parse all attributes of a node and apply custom handlers.
   * @param {Element} element
   */
  parseNodeAttributes(element) {
    const attributes = element.attributes;
    let n = attributes.length;
    while (n--) {
      const attr = attributes[n];
      this[attr.name.at(0)]?.(element, attr, attributes) ||
        (attr.value.startsWith("%") && this["%"]?.(element, attr));
    }
  },
};

/**
 * Tagged template function for creating DOM fragments with reactivity.
 * @param {TemplateStringsArray} strings
 * @param  {...any} values
 * @returns {DocumentFragment}
 */
function html(strings, ...values) {
  const markup = templateExtractor.extract(strings, ...values);
  const fragment = document.createRange().createContextualFragment(markup);
  for (const el of fragment.querySelectorAll("*")) {
    if (el.hasAttributes()) attributeBindingHandlers.parseNodeAttributes(el);
  }
  if (commentFragmentMap.size > 0) fragmentUtils.setChildFragment(fragment);
  clearAttributeFunctionMaps();
  return fragment;
}

/**
 * Clears all attribute/function/object maps after fragment creation.
 */
function clearAttributeFunctionMaps() {
  attributeFunctionMap.clear();
  functionMap.clear();
  objectMap.clear();
}

/**
 * Binds an array to a DOM fragment for reactive rendering.
 * @param {Array} array
 * @param {Function} renderFn
 * @param {Function} [updateItem]
 * @returns {Array}
 */
function bindArrayToFragment(array, renderFn, updateItem) {
  const nodes = array.map(renderFn);
  const comment = new Comment(` #${String(Math.random()).slice(9)} `);
  nodes.unshift(comment);
  if (array._react === undefined) {
    Object.defineProperty(array, "_react", {
      value: [{ comment, yieldfn: renderFn, updateItem }],
    });
  } else {
    array._react.push({ comment, yieldfn: renderFn, updateItem });
  }
  return nodes;
}

// --- Exports ---

export {
  queryTabs,
  getTab,
  createTab,
  removeTab,
  queryTabGroups,
  moveTabGroups,
  getSyncStorageItem,
  setSyncStorageItem,
  getSessionStorageItem,
  setSessionStorageItem,
  TAB_GROUP_COLORS,
  COLOR_RGB_MAP,
  THEME_BASE_URL,
  makeReactive,
  html,
  bindArrayToFragment,
};
